import { Metadata } from "next";
import { getTranslations } from 'next-intl/server';
import { SITE_NAME, SITE_URL } from "@/lib/constants";

type Props = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.tools.tools.mysqlPasswordGenerator' });
  
  const title = `${t('meta.title')} - ${SITE_NAME}`;
  const description = t('meta.description');
  const keywords = t('meta.keywords');
  
  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'website',
      url: `${SITE_URL}/${locale === 'zh' ? '' : locale + '/'}tools/mysql-password-generator`,
    },
    twitter: {
      card: 'summary',
      title,
      description,
    },
    alternates: {
      canonical: `${SITE_URL}/${locale === 'zh' ? '' : locale + '/'}tools/mysql-password-generator`,
      languages: {
        'zh': `${SITE_URL}/tools/mysql-password-generator`,
        'en': `${SITE_URL}/en/tools/mysql-password-generator`,
      },
    },
  };
}

export default function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}