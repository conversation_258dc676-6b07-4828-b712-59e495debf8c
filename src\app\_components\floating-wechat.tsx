"use client";

import { useState } from "react";
import Image from "next/image";
import { trackEvent } from "@/lib/gtag";

export function FloatingWechat() {
  const [showQR, setShowQR] = useState(false);

  return (
    <>
      {/* 悬浮按钮 */}
      <div className="fixed right-6 bottom-24 z-40">
        <button
          onClick={() => {
            setShowQR(true);
            trackEvent.wechatClick();
          }}
          className="group w-14 h-14 bg-green-500 hover:bg-green-600 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center text-white hover:scale-110"
          aria-label="微信公众号"
        >
          <svg className="w-7 h-7" fill="currentColor" viewBox="0 0 24 24">
            <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 4.882-1.932 7.621-.55-.302-2.676-2.476-4.991-5.748-6.364C9.691 2.477 9.196 2.188 8.691 2.188zM5.785 7.032c.446 0 .805.359.805.805 0 .446-.359.805-.805.805-.446 0-.805-.359-.805-.805 0-.446.359-.805.805-.805zM11.598 7.032c.446 0 .805.359.805.805 0 .446-.359.805-.805.805-.446 0-.805-.359-.805-.805 0-.446.359-.805.805-.805zM24 14.131c0-3.096-2.995-5.635-6.735-5.635S10.53 11.035 10.53 14.131c0 3.096 2.995 5.635 6.735 5.635a7.56 7.56 0 0 0 2.316-.403.864.864 0 0 1 .717.098l1.903 1.114a.326.326 0 0 0 .167.054.295.295 0 0 0 .29-.295c0-.072-.029-.143-.048-.213l-.39-1.48a.59.59 0 0 1 .213-.665C22.83 18.334 24 16.343 24 14.131zM15.693 12.587c-.446 0-.805.359-.805.805 0 .446.359.805.805.805.446 0 .805-.359.805-.805 0-.446-.359-.805-.805-.805zM19.572 12.587c-.446 0-.805.359-.805.805 0 .446.359.805.805.805.446 0 .805-.359.805-.805 0-.446-.359-.805-.805-.805z"/>
          </svg>
          
          {/* 提示文字 */}
          <div className="absolute right-full top-1/2 transform -translate-y-1/2 mr-3 bg-gray-900 text-white px-4 py-2 rounded-lg text-sm whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none shadow-lg min-w-max">
            微信公众号
            <div className="absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-900"></div>
          </div>
        </button>
      </div>

      {/* 二维码弹窗 */}
      {showQR && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl p-6 sm:p-8 max-w-sm w-full mx-auto relative animate-fade-in">
            {/* 关闭按钮 */}
            <button
              onClick={() => setShowQR(false)}
              className="absolute top-4 right-4 w-8 h-8 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors duration-200"
              aria-label="关闭"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            {/* 二维码内容 */}
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 4.882-1.932 7.621-.55-.302-2.676-2.476-4.991-5.748-6.364C9.691 2.477 9.196 2.188 8.691 2.188zM5.785 7.032c.446 0 .805.359.805.805 0 .446-.359.805-.805.805-.446 0-.805-.359-.805-.805 0-.446.359-.805.805-.805zM11.598 7.032c.446 0 .805.359.805.805 0 .446-.359.805-.805.805-.446 0-.805-.359-.805-.805 0-.446.359-.805.805-.805zM24 14.131c0-3.096-2.995-5.635-6.735-5.635S10.53 11.035 10.53 14.131c0 3.096 2.995 5.635 6.735 5.635a7.56 7.56 0 0 0 2.316-.403.864.864 0 0 1 .717.098l1.903 1.114a.326.326 0 0 0 .167.054.295.295 0 0 0 .29-.295c0-.072-.029-.143-.048-.213l-.39-1.48a.59.59 0 0 1 .213-.665C22.83 18.334 24 16.343 24 14.131zM15.693 12.587c-.446 0-.805.359-.805.805 0 .446.359.805.805.805.446 0 .805-.359.805-.805 0-.446-.359-.805-.805-.805zM19.572 12.587c-.446 0-.805.359-.805.805 0 .446.359.805.805.805.446 0 .805-.359.805-.805 0-.446-.359-.805-.805-.805z"/>
                </svg>
              </div>

              <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-2">关注微信公众号</h3>
              <p className="text-sm sm:text-base text-gray-600 mb-6">扫描二维码，获取最新技术文章推送</p>

              <div className="bg-gray-50 rounded-xl p-4 mb-6 flex justify-center">
                <Image
                  src="/images/qrcode.jpg"
                  alt="微信公众号二维码"
                  width={180}
                  height={180}
                  className="rounded-lg shadow-sm"
                />
              </div>

              <div className="space-y-2">
                <p className="text-sm text-gray-500 text-center">
                  使用微信扫一扫功能扫描上方二维码
                </p>
                <div className="flex items-center justify-center gap-2 text-xs text-gray-400">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  <span>每周推送优质技术内容</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
