import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { SITE_NAME, SITE_URL } from '@/lib/constants';

type Props = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.tools.tools.wechatAuth' });
  const tSite = await getTranslations({ locale, namespace: 'site' });
  
  const title = `${t('meta.title')} | ${tSite('name')}`;
  const description = t('meta.description');
  const url = locale === 'zh' ? `${SITE_URL}/tools/wechat-auth` : `${SITE_URL}/${locale}/tools/wechat-auth`;
  
  return {
    title,
    description,
    keywords: t('meta.keywords'),
    openGraph: {
      title,
      description,
      url,
      siteName: SITE_NAME,
      locale: locale,
      type: 'website',
      images: [
        {
          url: `${SITE_URL}/images/tools/wechat-auth-og.png`,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [`${SITE_URL}/images/tools/wechat-auth-og.png`],
    },
    alternates: {
      canonical: url,
      languages: {
        'zh': `${SITE_URL}/tools/wechat-auth`,
        'en': `${SITE_URL}/en/tools/wechat-auth`,
      },
    },
  };
}

export default function WechatAuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}