import { NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';

export async function GET() {
  try {
    const readmePath = join(process.cwd(), '_posts', 'README.md');
    const content = await readFile(readmePath, 'utf-8');
    
    return new NextResponse(content, {
      status: 200,
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'public, max-age=3600', // 缓存1小时
      },
    });
  } catch (error) {
    console.error('Failed to read README.md:', error);
    return new NextResponse('Failed to load README content', {
      status: 500,
    });
  }
}