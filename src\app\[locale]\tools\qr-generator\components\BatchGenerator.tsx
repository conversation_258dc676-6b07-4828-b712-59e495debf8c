import { useTranslations } from 'next-intl';

interface BatchGeneratorProps {
  batchData: string;
  setBatchData: (data: string) => void;
  batchResults: Array<{content: string, dataUrl: string}>;
  isGenerating: boolean;
  onBatchGenerate: () => void;
  onBatchDownload: () => void;
}

export default function BatchGenerator({
  batchData,
  setBatchData,
  batchResults,
  isGenerating,
  onBatchGenerate,
  onBatchDownload
}: BatchGeneratorProps) {
  const t = useTranslations('pages.tools.tools.qrGenerator');
  return (
    <>
      <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
          {t('batch.contentTitle')}
        </h2>
        <textarea
          value={batchData}
          onChange={(e) => setBatchData(e.target.value)}
          placeholder={t('batch.placeholder')}
          className="w-full h-40 px-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-slate-700 text-gray-900 dark:text-white resize-none"
        />
        <div className="mt-4 flex items-center justify-between">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {t('batch.itemCount', { count: batchData.split('\n').filter(line => line.trim()).length })}
          </span>
          <button
            onClick={onBatchGenerate}
            disabled={!batchData.trim() || isGenerating}
            className="px-6 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isGenerating ? t('batch.generating') : t('batch.generate')}
          </button>
        </div>
      </div>

      {batchResults.length > 0 && (
        <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              {t('batch.resultsTitle', { count: batchResults.length })}
            </h2>
            <button
              onClick={onBatchDownload}
              className="px-4 py-2 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 transition-colors"
            >
              {t('batch.downloadAll')}
            </button>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 max-h-96 overflow-y-auto">
            {batchResults.map((result, index) => (
              <div key={index} className="border border-gray-200 dark:border-slate-600 rounded-lg p-3">
                <img
                  src={result.dataUrl}
                  alt={`QR Code ${index + 1}`}
                  className="w-full aspect-square object-contain mb-2"
                />
                <p className="text-xs text-gray-600 dark:text-gray-400 truncate" title={result.content}>
                  {result.content}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}
    </>
  );
}