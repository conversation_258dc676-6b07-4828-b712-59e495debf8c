import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import createMiddleware from 'next-intl/middleware';
import { locales, defaultLocale } from './i18n';

// Create the next-intl middleware
const intlMiddleware = createMiddleware({
  locales,
  defaultLocale,
  localePrefix: 'as-needed'
});

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // 跳过 API 路由、静态文件和 Next.js 内部路径
  if (
    pathname.startsWith('/api/') ||
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/_vercel/') ||
    pathname.includes('.')
  ) {
    return NextResponse.next();
  }

  // 如果访问根路径 / 或其他没有语言前缀的路径
  if (pathname === '/' || (!pathname.startsWith('/en') && !pathname.startsWith('/zh'))) {
    // 重写到 /zh 页面，保持地址栏不变
    const url = request.nextUrl.clone();
    url.pathname = `/zh${pathname === '/' ? '' : pathname}`;
    return NextResponse.rewrite(url);
  }

  // 对于有语言前缀的路径，使用 next-intl 中间件处理
  return intlMiddleware(request);
}

export const config = {
  matcher: [
    // 匹配根路径
    '/',
    // 匹配所有路径，但排除 API 路由、Next.js 内部路径和静态文件
    '/((?!api|_next|_vercel|.*\\..*).*)'
  ]
};