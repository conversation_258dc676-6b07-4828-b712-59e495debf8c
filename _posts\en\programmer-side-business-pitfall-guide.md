---
excerpt: "5 major pitfalls programmers fall into when doing side projects: unclear requirements boundaries, underpricing, idealistic tech choices, trying to do everything alone, and lack of contracts. Hard-earned lessons to help you avoid these traps."
coverImage: "/assets/blog/41.png"
author:
  name: Lafu Code
  picture: "/assets/blog/authors/tim.jpeg"
featured: false
title: "5 Major Side Business Pitfalls Every Programmer Should Avoid - Have You Fallen Into Any?"
date: "2025-08-03"
---

# 5 Major Side Business Pitfalls Every Programmer Should Avoid - Have You Fallen Into Any?

When it comes to side businesses, I've basically crawled out of every possible pit. Started taking freelance projects over three years ago, thinking with my decent coding skills, making some extra cash shouldn't be too hard, right? Reality gave me a few harsh slaps.

The first time I took on a project, I was super confident, thinking building a website would be a piece of cake? Ended up dragging it out for 4 whole months, and when I calculated everything, I actually lost money. That's when I realized writing code and running a business are completely different beasts.

I've stepped into way too many pits over the years. Today I'm picking out the worst ones to share with you. If you're also thinking about doing side projects, maybe this can help you dodge some landmines.

---

## Pit #1: Only Talking Tech, Ignoring Requirements and Boundaries

This was the first and most painful pit I fell into.

In 2022, a friend referred me to their company to build a management dashboard. When I heard it was Vue + Node.js, I thought no problem and agreed on the spot. But during development:

- Client said "add a reporting feature while you're at it," I thought it was simple so I agreed, ended up spending a whole week just on that
- No UI mockups, client said "just make it look good," ended up revising the interface countless times and still not satisfied
- After finishing the features, client said "can you add an Excel export function?"

What was supposed to be a 2-week project ended up taking me 2 months on and off, with dozens of small requirements added along the way, but I was still paid the original quote.

**Lesson learned:**

Our programmer habits make us focus more on technical implementation, but when doing side projects, **requirement boundaries are 100 times more important than tech stack choices**. Clients don't understand tech - they just know "you can build websites," and to them, everything else seems like "just throw it in while you're at it."

Now I've learned to be smarter:

Before starting any project, I make a detailed feature list. What's included, what's not - everything needs to be crystal clear on paper. I also limit revision rounds, like 3 free revisions, anything beyond that costs extra. Client wants to add new features? No problem, let's recalculate time and cost.

Sounds nitpicky, but it saves everyone headaches.

---

## Pit #2: Pricing Too Low, Endless Work

I fell deep into this pit too.

When I first started doing side projects, I was always worried about scaring clients away with high prices. Once I had an e-commerce website project, estimated about 20 days of work, calculated based on my daily rate at the time, and quoted $1,200. Client agreed immediately, and I was secretly pleased.

The result? Project actually took 35 days, not including all the post-launch bug fixes and revisions. Calculated hourly, I was making less than $3/hour, below minimum wage.

**I made several mistakes:**

1. **Only calculated development time, ignored communication costs**: Daily client calls for requirements and progress updates - I didn't factor in any of that time
2. **Didn't consider revision and debugging time**: Thought once development was done, that was it, but post-launch adjustments took massive amounts of time
3. **No risk buffer**: When hitting technical roadblocks or requirement changes, I had zero time cushion

The worst part is, when you price too low, clients don't value you. They think "it's cheap anyway, a few more revisions won't hurt."

Later I developed a pricing method:

First estimate development time roughly, then multiply by 1.5, because communication, debugging, and bug fixes all eat up tons of time. Also add a project management fee, roughly 20-30% of total price - don't underestimate this part, just daily progress reports and requirement discussions are enough to keep you busy.

No matter how simple the project, I now start at $750 minimum. When money's too little, it's really not worth doing, and clients won't appreciate it either. I also changed payment terms: 30% upfront, 50% at halfway point, 20% on delivery. Lower risk this way.

What if it goes over time? Daily rate - I charge $75/day now.

---

## Pit #3: Overly "Idealistic" Tech Choices

As programmers, we all want to try new tech when we see it - nothing wrong with that. But client projects aren't your learning playground, and I learned this the hard way.

I have a painful lesson. Last year I took on a corporate website project. Could've used WordPress or traditional PHP framework and finished in a week. But I happened to want to learn Next.js, so I thought "perfect chance to practice."

The result?

- Spent a whole week just setting up the dev environment and learning basic concepts
- Ran into issues and spent several days researching and debugging
- During deployment, discovered Vercel was slow in China, spent half a day figuring that out
- Some traditional features the client wanted were actually more complex to implement with the new framework

A project that should've taken one week ended up taking me a month. Client was pushing for delivery, I was stressed, and the final code quality wasn't even good.

**Several tech choice pitfalls:**

1. **Using side projects as learning labs**: New tech definitely needs learning, but don't practice on client projects
2. **Chasing cutting-edge tech**: Clients want working products, not tech demos
3. **Ignoring deployment and maintenance costs**: Flashy tech stacks often have complex deployment and troublesome maintenance

Now my tech choices are super practical:

First rule is use what I know best. New tech might be cool, but client projects aren't for practice. If WordPress can solve it, definitely don't write code. If existing components work, definitely don't reinvent the wheel.

I basically stick to these combinations now:

- Corporate websites: straight WordPress, buy a theme and customize
- Admin dashboards: Vue + Element UI, with Express backend
- Mini-programs: native development tools, simple and crude
- APIs: Node.js + Koa gets it done

Tech stack feels old? Who cares - high efficiency, fewer bugs, happy clients.

---

## Pit #4: Trying to Handle Everything Yourself, Falling Into Manual Labor

Programmers are used to "if you want something done right, do it yourself." But when doing side projects, this habit can be disastrous.

My worst experience was taking on an e-commerce mini-program project. Client's budget was low, I thought "I can handle everything anyway," so I took on the whole thing:

- Requirements gathering and prototyping
- UI interface design
- Frontend development
- Backend API development
- Database design
- Server deployment
- Mini-program publishing
- Post-launch maintenance

Ended up working 7 PM to midnight every day after work, plus weekends. This went on for 2 months, and I felt like I was about to break. Family complained, health suffered, and the side project became a burden.

**Problems with doing everything:**

1. **Time costs spiral out of control**: Handling every step yourself doubles the time
2. **Not professional enough**: My UI design really wasn't as good as a professional designer's
3. **Easy to make mistakes**: One person handling too many aspects, easy to miss things
4. **Can't parallelize**: All work is sequential, extremely inefficient

Now I've learned to collaborate:

I have a designer friend, we often work together - he does UI, I do development, and we split project fees. Sometimes I'll find someone familiar to handle backend too, since doing full-stack alone is exhausting.

Also, use existing resources more. Buy UI templates from ThemeForest, use component libraries like Ant Design or Element UI - they're all mature. For servers, just use cloud services directly, don't build your own.

Benefits of proper division of labor: professionals doing professional work definitely beats me handling everything alone. Plus we can work in parallel, much higher efficiency. I also get more time to take on other projects.

Now I basically only handle the parts I'm best at - everything else gets outsourced if possible, or I use existing solutions.

---

## Pit #5: No Contracts, No Protection

This pit hurt me the most and left the deepest impression.

In 2021, a college classmate asked me to build an enterprise management system. Since we were old friends, I thought signing a contract would be too formal, so we just agreed verbally: features, price, delivery time - chatted a bit on WeChat and started working.

After working for over a month, I delivered on time. But my classmate said: "The system works, but the user experience isn't great, our boss isn't too satisfied. Can you optimize it a bit more?"

Thinking about our friendship, I spent another week optimizing. Then he said: "Can you add a data export function?" "Can you change the report styling?" ...

This dragged on for half a year, and finally he said: "Our company's cash flow is tight lately, can you take half payment now and the rest at year-end?"

To this day, I still haven't received that other half.

**Risks of no contracts:**

1. **Unlimited scope creep**: No clear boundaries, clients keep adding requirements
2. **Payment risk**: Verbal agreements have no legal force, can't do anything about unpaid bills
3. **Copyright risk**: Code ownership unclear, potential for misuse
4. **Disputes hard to resolve**: When disagreements arise, no written evidence

Now I definitely have contracts for projects:

First is project requirements document, clearly listing features and acceptance criteria. Then quotation including fees, payment terms, overtime charges, etc. Finally cooperation agreement - can be simplified but key info must be there.

Important points: project scope and deliverables, development timeline and acceptance standards, payment terms (I now insist on 30% upfront, 70% after acceptance), revision limits, code ownership, etc.

For tools, formal projects use Tencent e-signature, simple projects can use shared documents like Shimo. Worst case, WeChat chat records, but must have written confirmation.

Now even for $450 small projects, I have a simple written agreement. It's not about not trusting friends, but protecting everyone's interests.

---

## Final Thoughts

Doing side projects these years has really been equal parts blood, sweat, and laughter. Now while I wouldn't say I'm rich, at least I won't lose money on projects anymore.

I've stepped into every one of these pits, some multiple times. First didn't understand boundaries, then priced too low, then used new tech for practice and wasted time, plus wanting to do everything myself, and finally not signing contracts... Looking back, I was so naive then.

But these pits weren't stepped into for nothing - at least they taught me one thing: tech is just the foundation, doing side projects is more about doing business. You need to understand client psychology, manage projects, and protect yourself.

Now my side project income can reach half of my main job, and while it's not huge, my mindset has completely changed. It's no longer just selling time, but building my own small business system.

If you're also considering side projects, my advice is start small and summarize lessons each time. These pits are best avoided, but if you can't avoid them, don't get discouraged - stepping in pits is also growth.

When it comes to side projects, patience is more important than anything. Can't rush it, won't come fast. Take it slow, results will come.
