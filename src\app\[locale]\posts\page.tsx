import { Metadata } from "next";
import { getAllPosts } from "@/lib/api";
import { SITE_NAME, SITE_URL, HOME_OG_IMAGE_URL, DEFAULT_KEYWORDS } from "@/lib/constants";
import { getTranslations } from 'next-intl/server';
import PostsPageClient from './PostsPageClient';

type Props = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.posts' });

  const ogLocale = locale === 'zh' ? 'zh_CN' : 'en_US';
  const keywords = locale === 'zh'
    ? [...DEFAULT_KEYWORDS, ...t('metaKeywords').split(',')]
    : [...DEFAULT_KEYWORDS, ...t('metaKeywords').split(',')];

  return {
    title: `${t('title')} - ${SITE_NAME}`,
    description: t('metaDescription'),
    keywords: keywords,
    authors: [{ name: SITE_NAME, url: SITE_URL }],
    metadataBase: new URL(SITE_URL),
    alternates: {
      canonical: `/${locale === 'en' ? 'en/' : ''}posts`,
      languages: {
        'zh': '/posts',
        'en': '/en/posts',
      },
    },
    openGraph: {
      type: "website",
      locale: ogLocale,
      url: `${SITE_URL}/${locale === 'en' ? 'en/' : ''}posts`,
      title: `${t('title')} - ${SITE_NAME}`,
      description: t('ogDescription'),
      siteName: SITE_NAME,
      images: [
        {
          url: HOME_OG_IMAGE_URL,
          width: 1200,
          height: 630,
          alt: `${SITE_NAME} - ${t('ogTitle')}`,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: `${t('title')} - ${SITE_NAME}`,
      description: t('ogDescription'),
      images: [HOME_OG_IMAGE_URL],
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

export default async function PostsPage({ params }: Props) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.posts' });
  const searchT = await getTranslations({ locale, namespace: 'components.searchAndFilter' });
  const paginationT = await getTranslations({ locale, namespace: 'components.pagination' });

  // 获取当前语言的文章
  const allPosts = getAllPosts(locale);

  // 准备翻译数据，为 subtitle 提供 count 参数
  const translations = {
    title: t('title'),
    description: t('description'),
    subtitle: t('subtitle', { count: allPosts.length })
  };

  // 准备搜索和筛选组件的翻译
  const searchTranslations = {
    searchPlaceholder: searchT('searchPlaceholder'),
    sortByDate: searchT('sortByDate'),
    sortByTitle: searchT('sortByTitle'),
    filterAll: searchT('filterAll'),
    filterFeatured: searchT('filterFeatured'),
    resultsFoundPrefix: searchT('resultsFoundPrefix'),
    resultsFoundSuffix: searchT('resultsFoundSuffix'),
    resultsWithKeywordPrefix: searchT('resultsWithKeywordPrefix'),
    resultsWithKeywordSuffix: searchT('resultsWithKeywordSuffix'),
    noResultsTitle: searchT('noResultsTitle'),
    noResultsDescription: searchT('noResultsDescription'),
    resetFilters: searchT('resetFilters'),
    browseAllPosts: searchT('browseAllPosts')
  };

  const paginationTranslations = {
    previous: paginationT('previous'),
    next: paginationT('next'),
    showingItemsPrefix: paginationT('showingItemsPrefix'),
    showingItemsMiddle1: paginationT('showingItemsMiddle1'),
    showingItemsMiddle2: paginationT('showingItemsMiddle2'),
    showingItemsSuffix: paginationT('showingItemsSuffix'),
    pageInfoPrefix: paginationT('pageInfoPrefix'),
    pageInfoMiddle: paginationT('pageInfoMiddle'),
    pageInfoSuffix: paginationT('pageInfoSuffix')
  };

  return (
    <PostsPageClient
      locale={locale}
      translations={translations}
      posts={allPosts}
      searchTranslations={searchTranslations}
      paginationTranslations={paginationTranslations}
    />
  );
}
