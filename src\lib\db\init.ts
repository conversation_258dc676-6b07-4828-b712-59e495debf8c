import { db } from '@/lib/database'

// 创建数据库表的 SQL 语句
const createPostViewsTable = `
  CREATE TABLE IF NOT EXISTS post_views (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    slug TEXT NOT NULL UNIQUE,
    view_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  );
`

const createSlugIndex = `CREATE INDEX IF NOT EXISTS idx_post_views_slug ON post_views(slug);`
const createCountIndex = `CREATE INDEX IF NOT EXISTS idx_post_views_count ON post_views(view_count);`

// 初始化数据库表
export async function initDatabase() {
  try {
    // 在构建时跳过数据库初始化
    if (process.env.NEXT_PHASE === 'phase-production-build') {
      console.log('构建阶段，跳过数据库初始化')
      return true
    }

    console.log('正在检查数据库表...')

    // 检查表是否已存在
    const tableExists = await checkTablesExist()

    if (tableExists) {
      console.log('✓ post_views 表已存在，跳过创建')
      return true
    }

    console.log('正在创建数据库表...')

    // 创建表
    await db.execute(createPostViewsTable)
    console.log('✓ post_views 表创建成功')

    // 创建索引（分别执行）
    await db.execute(createSlugIndex)
    await db.execute(createCountIndex)
    console.log('✓ 索引创建成功')

    console.log('数据库初始化完成！')
    return true
  } catch (error) {
    console.error('数据库初始化失败:', error)
    // 在构建时不要因为数据库问题而失败
    if (process.env.NODE_ENV === 'production') {
      console.warn('生产环境构建时数据库连接失败，但继续构建')
      return true
    }
    return false
  }
}

// 检查表是否存在
export async function checkTablesExist(): Promise<boolean> {
  try {
    // 添加超时控制
    const timeoutPromise = new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error('数据库连接超时')), 5000)
    )

    const queryPromise = db.execute(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='post_views';
    `)

    const result = await Promise.race([queryPromise, timeoutPromise])
    return result.rows.length > 0
  } catch (error) {
    console.error('检查表存在性失败:', error)
    // 在构建时如果数据库连接失败，假设表不存在但不阻塞构建
    return false
  }
}
