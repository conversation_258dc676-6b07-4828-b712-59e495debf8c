import { type Author } from "./author";

export type Post = {
  slug: string;
  title: string;
  date: string;
  lastModified?: string;     // 最后修改时间（可选）
  coverImage: string;
  author: Author;
  excerpt: string;
  ogImage: {
    url: string;
  };
  content: string;
  preview?: boolean;
  // 精选文章相关字段
  featured?: boolean;        // 是否为精选文章
  featuredOrder?: number;    // 精选文章的排序权重
  featuredReason?: string;   // 精选理由（可选）
  // 阅读时长（分钟）
  readingTime?: number;      // 计算得出的阅读时长
};
