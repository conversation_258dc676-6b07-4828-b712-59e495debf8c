'use client'

import { useState, useCallback, useEffect } from 'react'
import { Copy, Clock, Calendar, Info, XCircle } from 'lucide-react'
import Link from 'next/link'
import { useTranslations, useLocale } from 'next-intl'
import { defaultLocale } from '@/i18n'

interface CronPart {
  field: string
  value: string
  description: string
  valid: boolean
}

interface ParsedCron {
  parts: CronPart[]
  description: string
  nextRuns: string[]
  isValid: boolean
  error?: string
}

export default function CronParserPage() {
  const [cronExpression, setCronExpression] = useState('')
  const [parsedResult, setParsedResult] = useState<ParsedCron | null>(null)

  const t = useTranslations('pages.tools.tools.cronParser')
  const locale = useLocale()

  // 根据当前语言生成正确的链接
  const getLocalizedHref = (path: string) => {
    if (locale === defaultLocale) {
      return path;
    }
    return `/${locale}${path}`;
  }

  // 将常量移到组件内部，以便使用翻译，并确保它们是数组
  const getTranslationSafely = (key: string, fallback: any) => {
    try {
      const result = t.raw(key)
      return Array.isArray(result) ? result : fallback
    } catch (error) {
      console.warn(`Translation missing for key: ${key}`, error)
      return fallback
    }
  }

  const CRON_EXAMPLES = getTranslationSafely('examples.list', [
    { expression: '0 0 * * *', description: locale === 'zh' ? '每天午夜执行' : 'Execute daily at midnight' },
    { expression: '0 9 * * 1-5', description: locale === 'zh' ? '工作日上午9点执行' : 'Execute at 9 AM on weekdays' },
    { expression: '*/15 * * * *', description: locale === 'zh' ? '每15分钟执行一次' : 'Execute every 15 minutes' },
    { expression: '0 0 1 * *', description: locale === 'zh' ? '每月1号午夜执行' : 'Execute at midnight on the 1st of every month' },
    { expression: '0 0 * * 0', description: locale === 'zh' ? '每周日午夜执行' : 'Execute every Sunday at midnight' },
    { expression: '30 2 * * *', description: locale === 'zh' ? '每天凌晨2:30执行' : 'Execute daily at 2:30 AM' },
    { expression: '0 */6 * * *', description: locale === 'zh' ? '每6小时执行一次' : 'Execute every 6 hours' },
    { expression: '0 0 1 1 *', description: locale === 'zh' ? '每年1月1日执行' : 'Execute on January 1st every year' }
  ])

  const FIELD_NAMES = getTranslationSafely('fields.names',
    locale === 'zh' ? ['分钟', '小时', '日', '月', '星期'] : ['Minute', 'Hour', 'Day', 'Month', 'Weekday']
  )

  const FIELD_RANGES = getTranslationSafely('fields.ranges', [
    '0-59', '0-23', '1-31', '1-12',
    locale === 'zh' ? '0-7 (0和7都表示周日)' : '0-7 (0 and 7 both represent Sunday)'
  ])

  // 动态设置页面标题
  useEffect(() => {
    document.title = t('meta.title')
  }, [t])

  const parseCronExpression = useCallback((expression: string): ParsedCron => {
    const parts = expression.trim().split(/\s+/)
    
    if (parts.length !== 5) {
      return {
        parts: [],
        description: '',
        nextRuns: [],
        isValid: false,
        error: t('errors.invalidFields')
      }
    }

    const validateCronField = (value: string, fieldIndex: number): boolean => {
      if (value === '*') return true
      if (value.includes('/')) {
        const [range, step] = value.split('/')
        if (range === '*' || validateRange(range, fieldIndex)) {
          const stepNum = parseInt(step)
          return !isNaN(stepNum) && stepNum > 0
        }
        return false
      }
      if (value.includes('-')) {
        const [start, end] = value.split('-')
        return validateSingleValue(start, fieldIndex) && validateSingleValue(end, fieldIndex)
      }
      if (value.includes(',')) {
        return value.split(',').every(v => validateSingleValue(v.trim(), fieldIndex))
      }
      return validateSingleValue(value, fieldIndex)
    }

    const validateSingleValue = (value: string, fieldIndex: number): boolean => {
      const num = parseInt(value)
      if (isNaN(num)) return false
      
      const ranges = [
        [0, 59], // 分钟
        [0, 23], // 小时
        [1, 31], // 日
        [1, 12], // 月
        [0, 7]   // 星期
      ]
      
      const [min, max] = ranges[fieldIndex]
      return num >= min && num <= max
    }

    const validateRange = (range: string, fieldIndex: number): boolean => {
      if (range === '*') return true
      return validateSingleValue(range, fieldIndex)
    }

    const generateDescription = (parts: string[]): string => {
      const [minute, hour, day, month, weekday] = parts

      let desc = t('timeDescriptions.executeTime')

      // 分钟
      if (minute === '*') {
        desc += t('timeDescriptions.everyMinute')
      } else if (minute.includes('/')) {
        const step = minute.split('/')[1]
        desc += t('timeDescriptions.everyNMinutes', { n: step })
      } else {
        desc += t('timeDescriptions.minuteN', { n: minute })
      }

      // 小时
      if (hour === '*') {
        desc += t('timeDescriptions.everyHour')
      } else if (hour.includes('/')) {
        const step = hour.split('/')[1]
        desc += t('timeDescriptions.everyNHours', { n: step })
      } else {
        desc += t('timeDescriptions.hourN', { n: hour })
      }

      // 日期和星期
      if (day !== '*' && weekday !== '*') {
        desc += t('timeDescriptions.dayAndWeek', {
          day: day,
          weekday: getWeekdayName(weekday)
        })
      } else if (day !== '*') {
        desc += t('timeDescriptions.dayN', { day: day })
      } else if (weekday !== '*') {
        desc += t('timeDescriptions.weekN', { weekday: getWeekdayName(weekday) })
      }

      // 月份
      if (month !== '*') {
        desc += t('timeDescriptions.monthN', { month: month })
      }

      return desc
    }

    const getWeekdayName = (weekday: string): string => {
      const names = t.raw('weekdays')
      const num = parseInt(weekday)
      return isNaN(num) ? weekday : names[num]
    }

    const generateNextRuns = (parts: string[]): string[] => {
      const [minute, hour, day, month, weekday] = parts
      const now = new Date()
      const nextRuns: string[] = []
      
      // 解析各个字段的值
      const parseField = (field: string, fieldType: string): number[] => {
        if (field === '*') return []
        
        if (field.includes(',')) {
          return field.split(',').map(v => parseInt(v.trim())).filter(v => !isNaN(v))
        }
        
        if (field.includes('-')) {
          const [start, end] = field.split('-').map(v => parseInt(v.trim()))
          const result = []
          for (let i = start; i <= end; i++) {
            result.push(i)
          }
          return result
        }
        
        if (field.includes('/')) {
          const [base, step] = field.split('/')
          const stepNum = parseInt(step)
          if (base === '*') {
            const result = []
            let max = 59
            if (fieldType === 'hour') max = 23
            else if (fieldType === 'day') max = 31
            else if (fieldType === 'month') max = 12
            else if (fieldType === 'weekday') max = 6
            
            const start = fieldType === 'day' || fieldType === 'month' ? 1 : 0
            for (let i = start; i <= max; i += stepNum) {
              result.push(i)
            }
            return result
          }
        }
        
        const num = parseInt(field)
        return isNaN(num) ? [] : [num]
      }
      
      const minuteValues = parseField(minute, 'minute')
      const hourValues = parseField(hour, 'hour')
      const dayValues = parseField(day, 'day')
      const monthValues = parseField(month, 'month')
      const weekdayValues = parseField(weekday, 'weekday')
      
      // 从下一分钟开始计算
      const startTime = new Date(now)
      startTime.setMinutes(startTime.getMinutes() + 1)
      startTime.setSeconds(0, 0)
      
      // 最多检查未来7天，每分钟检查一次
      const maxChecks = 7 * 24 * 60
      
      for (let i = 0; i < maxChecks && nextRuns.length < 5; i++) {
        const testTime = new Date(startTime.getTime() + i * 60 * 1000)
        
        const testMinute = testTime.getMinutes()
        const testHour = testTime.getHours()
        const testDay = testTime.getDate()
        const testMonth = testTime.getMonth() + 1
        const testWeekday = testTime.getDay()
        
        // 检查是否匹配所有字段
        const minuteMatch = minuteValues.length === 0 || minuteValues.includes(testMinute)
        const hourMatch = hourValues.length === 0 || hourValues.includes(testHour)
        const dayMatch = dayValues.length === 0 || dayValues.includes(testDay)
        const monthMatch = monthValues.length === 0 || monthValues.includes(testMonth)
        const weekdayMatch = weekdayValues.length === 0 || weekdayValues.includes(testWeekday)
        
        if (minuteMatch && hourMatch && dayMatch && monthMatch && weekdayMatch) {
          const localeCode = locale === 'zh' ? 'zh-CN' : 'en-US'
          nextRuns.push(testTime.toLocaleString(localeCode, {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            weekday: 'short'
          }))
        }
      }
      
      // 如果没有找到匹配的时间，返回提示信息
      if (nextRuns.length === 0) {
        return [t('errors.noMatches')]
      }
      
      return nextRuns
    }

    const cronParts: CronPart[] = parts.map((part, index) => ({
      field: FIELD_NAMES[index],
      value: part,
      description: part === '*' ? t('timeDescriptions.anyValue') : part,
      valid: validateCronField(part, index)
    }))

    const isValid = cronParts.every(part => part.valid)
    
    if (!isValid) {
      return {
        parts: cronParts,
        description: '',
        nextRuns: [],
        isValid: false,
        error: t('errors.invalidFormat')
      }
    }

    return {
      parts: cronParts,
      description: generateDescription(parts),
      nextRuns: generateNextRuns(parts),
      isValid: true
    }
  }, [])

  const copyToClipboard = useCallback(async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
    } catch {
      console.error(t('errors.copyFailed'))
    }
  }, [])

  const handleParse = useCallback(() => {
    if (!cronExpression.trim()) {
      setParsedResult(null)
      return
    }
    
    try {
      const result = parseCronExpression(cronExpression.trim())
      setParsedResult(result)
    } catch {
      setParsedResult(null)
    }
  }, [cronExpression, parseCronExpression])

  const handleExampleClick = useCallback((expression: string) => {
    setCronExpression(expression)
    const result = parseCronExpression(expression)
    setParsedResult(result)
  }, [parseCronExpression])

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <div className="container-custom py-20">
        {/* 面包屑导航 */}
        <nav className="flex mb-8" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <Link href={getLocalizedHref('/')} className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                <svg className="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                  <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                </svg>
{t('breadcrumb.home')}
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                  <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                </svg>
                <Link href={getLocalizedHref('/tools')} className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">
                  {t('breadcrumb.tools')}
                </Link>
              </div>
            </li>
            <li aria-current="page">
              <div className="flex items-center">
                <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                  <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                </svg>
                <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">{t('breadcrumb.current')}</span>
              </div>
            </li>
          </ol>
        </nav>

        {/* 页面标题 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            {t('title')}
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            {t('description')}
          </p>
        </div>

        {/* 主要内容 */}
        <div className="max-w-7xl mx-auto">
         <div className="grid lg:grid-cols-3 gap-6">
           {/* 左侧：输入和结果区域 */}
           <div className="lg:col-span-2 space-y-6">
             {/* 输入区域 */}
        <div className="bg-white rounded-lg border shadow-sm">
          <div className="p-6 pb-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Clock className="h-5 w-5" />
              {t('input.title')}
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              {t('input.description')}
            </p>
          </div>
          <div className="px-6 pb-6 space-y-4">
            <div className="flex gap-2">
              <input
                type="text"
                placeholder={t('input.placeholder')}
                value={cronExpression}
                onChange={(e) => setCronExpression(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleParse()}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono"
              />
              <button
                onClick={handleParse}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
{t('actions.parse')}
              </button>
            </div>
            
            {/* 字段说明 */}
            <div className="grid grid-cols-5 gap-2 text-sm">
              {FIELD_NAMES.map((name, index) => (
                <div key={name} className="text-center p-2 bg-gray-100 rounded">
                  <div className="font-medium">{name}</div>
                  <div className="text-xs text-gray-600">
                    {FIELD_RANGES[index]}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

            {/* 解析结果 */}
            {parsedResult && (
              <div className="bg-white rounded-lg border shadow-sm">
                <div className="p-6 pb-4">
                  <h3 className="text-lg font-semibold flex items-center gap-2">
                    <Info className="h-5 w-5" />
                    {t('result.title')}
                  </h3>
                </div>
                <div className="px-6 pb-6 space-y-4">
                  {parsedResult.error ? (
                    <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                      <div className="flex items-center">
                        <XCircle className="h-4 w-4 text-red-500 mr-2" />
                        <span className="text-red-700">{parsedResult.error}</span>
                      </div>
                    </div>
                  ) : (
                    <>
                      {/* 字段解析 */}
                      <div className="space-y-2">
                        <h4 className="font-medium">{t('result.fieldsTitle')}</h4>
                        <div className="grid gap-2">
                          {parsedResult.parts.map((part, index) => (
                            <div key={index} className="flex items-center justify-between p-2 border rounded">
                              <div className="flex items-center gap-2">
                                <span className={`px-2 py-1 rounded text-xs font-medium ${
                                  part.valid 
                                    ? 'bg-blue-100 text-blue-800' 
                                    : 'bg-red-100 text-red-800'
                                }`}>
                                  {part.field}
                                </span>
                                <code className="px-2 py-1 bg-gray-100 rounded text-sm">
                                  {part.value}
                                </code>
                              </div>
                              <span className="text-sm text-gray-600">
                                {part.description}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* 执行描述 */}
                      <div className="space-y-2">
                        <h4 className="font-medium">{t('result.patternTitle')}</h4>
                        <div className="p-3 bg-gray-100 rounded">
                          {parsedResult.description}
                        </div>
                      </div>

                      {/* 下次执行时间 */}
                      <div className="space-y-2">
                        <h4 className="font-medium flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
{t('result.nextRunsTitle')}
                        </h4>
                        <div className="grid gap-2">
                          {parsedResult.nextRuns.map((time, index) => (
                            <div key={index} className="flex items-center justify-between p-2 border rounded">
                              <span className="font-mono">{time}</span>
                              <button
                                onClick={() => copyToClipboard(time)}
                                className="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded"
                                title={t('actions.copyTitle')}
                              >
                                <Copy className="h-4 w-4" />
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* 右侧：常用示例和语法说明 */}
          <div className="space-y-6">
            {/* 常用示例 */}
            <div className="bg-white rounded-lg border shadow-sm">
              <div className="p-6 pb-4">
                <h3 className="text-lg font-semibold">{t('examples.title')}</h3>
                <p className="text-sm text-gray-600 mt-1">
                  {t('examples.description')}
                </p>
              </div>
              <div className="px-6 pb-6">
                <div className="space-y-2">
                  {CRON_EXAMPLES.map((example, index) => (
                    <div
                      key={index}
                      className="p-3 border rounded cursor-pointer hover:bg-gray-50 transition-colors"
                      onClick={() => handleExampleClick(example.expression)}
                    >
                      <code className="block px-2 py-1 bg-gray-100 rounded text-sm font-mono mb-1">
                        {example.expression}
                      </code>
                      <span className="text-xs text-gray-600">
                        {example.description}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* 语法说明 */}
            <div className="bg-white rounded-lg border shadow-sm">
              <div className="p-6 pb-4">
                <h3 className="text-lg font-semibold">{t('syntax.title')}</h3>
              </div>
              <div className="px-6 pb-6">
                <div className="space-y-3 text-sm">
                  <div>
                    <div className="font-medium mb-1">{t('syntax.format')}</div>
                    <code className="px-2 py-1 bg-gray-100 rounded text-xs">
                      {t('syntax.formatValue')}
                    </code>
                  </div>
                  <div>
                    <div className="font-medium mb-1">{t('syntax.specialChars')}</div>
                    <div className="space-y-1 text-xs">
                      <div><code className="px-1 bg-gray-100 rounded">*</code> - {t('syntax.chars.asterisk')}</div>
                      <div><code className="px-1 bg-gray-100 rounded">?</code> - {t('syntax.chars.question')}</div>
                      <div><code className="px-1 bg-gray-100 rounded">-</code> - {t('syntax.chars.dash')}</div>
                      <div><code className="px-1 bg-gray-100 rounded">,</code> - {t('syntax.chars.comma')}</div>
                      <div><code className="px-1 bg-gray-100 rounded">/</code> - {t('syntax.chars.slash')}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        </div>
      </div>
    </div>
  )
}