"use client";

import { Suspense } from 'react';
import { usePageTracking } from '@/hooks/usePageTracking';

function PageTracker() {
  usePageTracking();
  return null;
}

interface PageTrackingProviderProps {
  children: React.ReactNode;
}

export function PageTrackingProvider({ children }: PageTrackingProviderProps) {
  return (
    <>
      <Suspense fallback={null}>
        <PageTracker />
      </Suspense>
      {children}
    </>
  );
}
