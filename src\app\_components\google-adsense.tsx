"use client";

import Script from 'next/script';
import { useEffect } from 'react';

const ADSENSE_CLIENT_ID = 'ca-pub-2643320070750503';

export function GoogleAdSense() {
  useEffect(() => {
    // 在开发环境下不加载 AdSense
    if (process.env.NODE_ENV !== 'production') {
      return;
    }

    // 检查是否已经加载过 AdSense
    if (typeof window !== 'undefined' && window.adsbygoogle) {
      return;
    }

    // 添加错误处理
    const handleAdSenseError = (error: ErrorEvent) => {
      // 静默处理 AdSense 相关错误，避免在控制台显示
      if (error.filename && (
        error.filename.includes('doubleclick.net') ||
        error.filename.includes('googlesyndication.com') ||
        error.filename.includes('googletagservices.com')
      )) {
        error.preventDefault();
        return false;
      }
    };

    // 添加全局错误监听器
    window.addEventListener('error', handleAdSenseError);

    // 清理函数
    return () => {
      window.removeEventListener('error', handleAdSenseError);
    };
  }, []);

  // 在开发环境下不渲染
  if (process.env.NODE_ENV !== 'production') {
    return null;
  }

  return (
    <>
      <Script
        id="google-adsense"
        src={`https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${ADSENSE_CLIENT_ID}`}
        crossOrigin="anonymous"
        strategy="afterInteractive"
        onError={(error) => {
          // 静默处理脚本加载错误
          console.debug('AdSense script load error (this is normal during development):', error);
        }}
        onLoad={() => {
          // 脚本加载成功后的处理
          if (typeof window !== 'undefined' && window.adsbygoogle) {
            try {
              // 初始化 AdSense
              (window.adsbygoogle = window.adsbygoogle || []).push({});
            } catch (error) {
              // 静默处理初始化错误
              console.debug('AdSense initialization error:', error);
            }
          }
        }}
      />
    </>
  );
}

// 类型声明
declare global {
  interface Window {
    adsbygoogle: any[];
  }
}
