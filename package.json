{"private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "db:init": "npx tsx scripts/init-db.ts"}, "dependencies": {"@giscus/react": "^3.1.0", "@libsql/client": "^0.15.9", "@tailwindcss/postcss": "^4.1.11", "@types/prismjs": "^1.26.5", "@types/qrcode": "^1.5.5", "classnames": "^2.5.1", "date-fns": "^3.6.0", "dotenv": "^17.0.1", "gray-matter": "^4.0.3", "lucide-react": "^0.525.0", "mermaid": "^11.8.0", "next": "15.0.2", "next-intl": "^4.3.4", "next-themes": "^0.4.6", "prismjs": "^1.30.0", "qrcode": "^1.5.4", "react": "19.0.0-rc-02c0e824-20241028", "react-dom": "19.0.0-rc-02c0e824-20241028", "rehype-autolink-headings": "^7.1.0", "rehype-highlight": "^7.0.2", "rehype-slug": "^6.0.0", "rehype-stringify": "^10.0.1", "remark": "^15.0.1", "remark-gfm": "^4.0.1", "remark-html": "^16.0.1", "remark-rehype": "^11.1.2", "zod": "^3.25.70"}, "devDependencies": {"@types/node": "^20.14.8", "@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.5.2"}}