import Link from "next/link";
import { type Author } from "@/interfaces/author";
import CoverImage from "./cover-image";
import LocalizedDateFormatter from "./localized-date-formatter";
import { ReadingTime } from "./reading-time";

type Post = {
  slug: string;
  title: string;
  date: string;
  coverImage: string;
  author: Author;
  excerpt: string;
  content: string;
};

type Props = {
  posts: Post[];
};

export function LatestPosts({ posts }: Props) {
  return (
    <section id="latest" className="py-24 md:py-32 relative">
      {/* 背景装饰 */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-0 left-1/4 w-80 h-80 bg-gradient-to-br from-cyan-200/30 to-blue-200/30 dark:from-cyan-800/20 dark:to-blue-800/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-72 h-72 bg-gradient-to-br from-green-200/30 to-emerald-200/30 dark:from-green-800/20 dark:to-emerald-800/20 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* 标题区域 */}
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-2 bg-gradient-to-r from-cyan-100 to-blue-100 dark:from-cyan-900/30 dark:to-blue-900/30 text-cyan-700 dark:text-cyan-300 rounded-full text-sm font-medium mb-4">
              📚 最新文章
            </span>
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-slate-900 dark:text-white mb-6">
              持续<span className="text-gradient-primary">更新</span>的技术内容
            </h2>
            <p className="text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto leading-relaxed">
              探索最新的技术趋势，学习前沿的开发技巧，与时俱进的技术分享
            </p>
          </div>

          {/* 文章网格 */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 md:gap-6 lg:gap-8">
            {posts.map((post, index) => (
              <article
                key={post.slug}
                className="group bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 border border-slate-200/50 dark:border-slate-700/50 hover:border-cyan-300/50 dark:hover:border-cyan-600/50"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                {/* 封面图片 */}
                <div className="relative h-40 sm:h-44 md:h-40 lg:h-44 xl:h-40 overflow-hidden">
                  <CoverImage
                    slug={post.slug}
                    title={post.title}
                    src={post.coverImage}
                  />
                  
                  {/* 渐变遮罩 */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  
                  {/* 新文章标签 */}
                  {index < 2 && (
                    <div className="absolute top-3 right-3">
                      <span className="px-2 py-1 bg-gradient-to-r from-green-400 to-emerald-400 text-white text-xs font-medium rounded-full shadow-lg">
                        🆕 NEW
                      </span>
                    </div>
                  )}
                  
                  {/* 悬浮阅读按钮 */}
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-4 group-hover:translate-y-0">
                    <Link
                      href={`/posts/${post.slug}`}
                      className="btn-3d text-sm"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      阅读
                    </Link>
                  </div>
                </div>

                {/* 内容区域 */}
                <div className="p-4 lg:p-5 xl:p-6">
                  {/* 日期和分类 */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-cyan-500 rounded-full"></div>
                      <span className="text-sm text-slate-500 dark:text-slate-400 font-medium">
                        <LocalizedDateFormatter dateString={post.date} />
                      </span>
                    </div>
                    
                    {/* 阅读时间估算 */}
                    <ReadingTime
                      content={post.content}
                      format="full"
                      className="text-xs text-slate-400 dark:text-slate-500 bg-slate-100 dark:bg-slate-700 px-2 py-1 rounded-full"
                    />
                  </div>

                  {/* 标题 */}
                  <h3 className="text-base md:text-lg lg:text-xl font-bold leading-tight mb-3 text-slate-900 dark:text-white group-hover:text-cyan-600 dark:group-hover:text-cyan-400 transition-colors duration-300">
                    <Link href={`/posts/${post.slug}`}>
                      {post.title}
                    </Link>
                  </h3>

                  {/* 摘要 */}
                  <p className="text-xs md:text-sm text-slate-600 dark:text-slate-300 leading-relaxed mb-4 line-clamp-2 lg:line-clamp-3">
                    {post.excerpt}
                  </p>

                  {/* 底部信息 */}
                  <div className="flex items-center justify-between">
                    {/* 作者信息 */}
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-cyan-500 to-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                        {post.author.name.charAt(0)}
                      </div>
                      <div>
                        <p className="text-sm font-medium text-slate-900 dark:text-white">
                          {post.author.name}
                        </p>
                      </div>
                    </div>

                    {/* 阅读更多 */}
                    <Link
                      href={`/posts/${post.slug}`}
                      className="text-cyan-600 dark:text-cyan-400 hover:text-cyan-700 dark:hover:text-cyan-300 text-sm font-medium flex items-center gap-1 group/link"
                    >
                      阅读
                      <svg className="w-4 h-4 transform group-hover/link:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                      </svg>
                    </Link>
                  </div>
                </div>

                {/* 底部装饰线 */}
                <div className="h-1 bg-gradient-to-r from-cyan-500 to-blue-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              </article>
            ))}
          </div>

          {/* 查看全部按钮 */}
          <div className="text-center mt-16">
            <div className="bg-gradient-to-r from-slate-50 to-cyan-50 dark:from-slate-800 dark:to-slate-700 rounded-2xl p-8 border border-slate-200/50 dark:border-slate-600/50">
              <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">
                还有更多精彩内容
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-6 max-w-2xl mx-auto">
                这只是冰山一角，我们还有更多深度技术文章等你来探索
              </p>
              <Link
                href="/posts"
                className="btn-glow group px-8 py-4 text-lg font-semibold"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                查看全部文章
                <svg className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
