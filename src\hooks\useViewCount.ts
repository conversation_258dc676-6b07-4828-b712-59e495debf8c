"use client";

import { useState, useEffect, useCallback } from 'react';

interface ViewCountData {
  slug: string;
  views: number;
  success: boolean;
}

interface UseViewCountReturn {
  views: number;
  loading: boolean;
  error: string | null;
  incrementView: () => Promise<void>;
}

export function useViewCount(slug: string): UseViewCountReturn {
  const [views, setViews] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // 获取访问次数
  const fetchViewCount = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/views/${encodeURIComponent(slug)}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: ViewCountData = await response.json();

      if (data.success) {
        setViews(data.views);
      } else {
        throw new Error('Failed to fetch view count');
      }
    } catch (err) {
      console.error('Error fetching view count:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      setViews(0); // 设置默认值
    } finally {
      setLoading(false);
    }
  }, [slug]);

  // 增加访问次数
  const incrementView = useCallback(async () => {
    try {
      const response = await fetch(`/api/views/${encodeURIComponent(slug)}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: ViewCountData = await response.json();

      if (data.success) {
        setViews(data.views);
      } else {
        throw new Error('Failed to increment view count');
      }
    } catch (err) {
      console.error('Error incrementing view count:', err);
      // 不设置错误状态，因为这是后台操作
    }
  }, [slug]);

  // 组件挂载时获取访问次数
  useEffect(() => {
    if (slug) {
      fetchViewCount();
    }
  }, [slug, fetchViewCount]);

  // 页面访问时增加访问次数（延迟执行，避免影响页面加载）
  useEffect(() => {
    if (slug && !loading && !error) {
      const timer = setTimeout(() => {
        incrementView();
      }, 1000); // 1秒后增加访问次数

      return () => clearTimeout(timer);
    }
  }, [slug, loading, error, incrementView]);

  return {
    views,
    loading,
    error,
    incrementView,
  };
}

// 格式化访问次数显示
export function formatViewCount(count: number): string {
  if (count < 1000) {
    return count.toString();
  } else if (count < 10000) {
    return `${(count / 1000).toFixed(1)}k`;
  } else if (count < 1000000) {
    return `${Math.floor(count / 1000)}k`;
  } else {
    return `${(count / 1000000).toFixed(1)}M`;
  }
}

// 批量获取多个文章的访问次数
export async function fetchMultipleViewCounts(slugs: string[]): Promise<Record<string, number>> {
  try {
    const response = await fetch('/api/views/batch', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ slugs }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    if (data.success) {
      const result: Record<string, number> = {};
      data.results.forEach((item: { slug: string; views: number }) => {
        result[item.slug] = item.views;
      });
      return result;
    } else {
      throw new Error('Failed to fetch view counts');
    }
  } catch (err) {
    console.error('Error fetching multiple view counts:', err);
    return {};
  }
}
