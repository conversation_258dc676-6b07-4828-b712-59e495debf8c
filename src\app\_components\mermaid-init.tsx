"use client";

import { useEffect } from 'react';

// 全局标记，确保只初始化一次
declare global {
  interface Window {
    mermaidInitialized?: boolean;
    initMermaid?: () => void;
  }
}

export function MermaidInit() {
  useEffect(() => {
    // 如果已经初始化过，直接返回
    if (window.mermaidInitialized) {
      return;
    }

    const initMermaid = async () => {
      try {
        // 标记为正在初始化，防止重复
        window.mermaidInitialized = true;

        // 动态导入mermaid
        const mermaid = (await import('mermaid')).default;

        // 初始化配置
        mermaid.initialize({
          startOnLoad: false,
          theme: 'default',
          securityLevel: 'loose',
          fontFamily: 'ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif',
          fontSize: 14,
          flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'basis'
          },
          pie: {
            useMaxWidth: true
          },
          sequence: {
            useMaxWidth: true,
            diagramMarginX: 50,
            diagramMarginY: 10
          },
          class: {
            useMaxWidth: true
          },
          gantt: {
            useMaxWidth: true
          }
        });

        // 处理函数
        const processMermaidCharts = async () => {
          // 清理所有已渲染的图表
          const existingContainers = document.querySelectorAll('[data-mermaid-rendered="true"]');
          existingContainers.forEach(container => container.remove());

          // 隐藏所有原始mermaid代码块
          const mermaidBlocks = document.querySelectorAll('pre code.language-mermaid');
          mermaidBlocks.forEach(block => {
            const preElement = block.closest('pre') as HTMLElement;
            if (preElement) {
              preElement.style.display = 'none';
              preElement.style.visibility = 'hidden';
              preElement.style.height = '0';
              preElement.style.overflow = 'hidden';
              preElement.style.margin = '0';
              preElement.style.padding = '0';
              preElement.classList.add('mermaid-processed');
            }
          });

          // 处理占位符
          const placeholders = document.querySelectorAll('.mermaid-placeholder');
          for (let i = 0; i < placeholders.length; i++) {
            const placeholder = placeholders[i] as HTMLElement;
            const encodedCode = placeholder.getAttribute('data-mermaid-code');
            const chartId = placeholder.getAttribute('data-chart-id') || `mermaid-placeholder-${i}`;

            if (!encodedCode) continue;

            try {
              const chart = decodeURIComponent(encodedCode);

              // 创建容器
              const container = document.createElement('div');
              container.className = 'mermaid-container my-6 flex justify-center overflow-x-auto p-4 bg-white border border-gray-200 rounded-lg';
              container.setAttribute('data-mermaid-rendered', 'true');

              // 验证语法并渲染
              const isValid = await mermaid.parse(chart);
              if (isValid) {
                // 渲染图表
                const { svg } = await mermaid.render(chartId, chart);
                container.innerHTML = svg;

                // 添加响应式样式
                const svgElement = container.querySelector('svg');
                if (svgElement) {
                  svgElement.style.maxWidth = '100%';
                  svgElement.style.height = 'auto';
                  svgElement.style.display = 'block';
                  svgElement.style.margin = '0 auto';
                }

                // 替换占位符
                placeholder.parentNode?.replaceChild(container, placeholder);
              } else {
                // 如果语法无效，移除占位符
                placeholder.remove();
              }
            } catch (error) {
              console.error('Mermaid rendering error:', error);
              placeholder.remove();
            }
          }
        };

        // 延迟执行，确保DOM完全加载
        setTimeout(processMermaidCharts, 300);

      } catch (error) {
        console.error('Failed to initialize Mermaid:', error);
        // 重置标记，允许重试
        window.mermaidInitialized = false;
      }
    };

    initMermaid();
  }, []);

  return null;
}
