{"title": "🔗 微信和企微授权链接生成器", "description": "快速生成微信公众号和企业微信的OAuth授权链接", "breadcrumb": {"home": "首页", "tools": "工具箱", "current": "微信授权链接生成器"}, "authType": {"title": "授权类型", "wechat": "微信公众号", "wechatDesc": "适用于微信公众号网页授权", "workWechat": "企业微信", "workWechatDesc": "适用于企业内部开发应用授权"}, "config": {"title": "配置参数", "loadExample": "加载示例", "appId": "应用ID (AppID)", "corpId": "企业ID (CorpID)", "redirectUri": "回调地址", "agentId": "应用AgentID", "scope": "授权作用域", "responseType": "响应类型", "state": "自定义参数", "stateDesc": "用于保持请求和回调的状态，授权请求后原样带回给第三方", "showAdvanced": "显示高级设置", "hideAdvanced": "隐藏高级设置", "scopes": {"snsapi_base": "snsapi_base - 静默授权，可获取成员的基础信息（UserId）", "snsapi_userinfo": "snsapi_userinfo - 弹出授权页面，可获取用户基本信息", "snsapi_privateinfo": "snsapi_privateinfo - 手动授权，可获取成员的详细信息，包含头像、二维码等敏感信息"}}, "result": {"title": "生成结果", "generatedUrl": "生成的授权链接：", "placeholder": "请填写必要参数以生成授权链接"}, "actions": {"copy": "复制链接", "clear": "清空表单", "testUrl": "在新窗口测试链接"}, "messages": {"copySuccess": "授权链接已复制到剪贴板！", "copyFailed": "复制失败，请手动复制链接"}, "instructions": {"title": "使用说明", "step1": "选择授权类型：微信公众号或企业微信", "step2": "填写应用ID和回调地址等必要参数", "step3": "选择合适的授权作用域和其他可选参数", "step4": "复制生成的授权链接，集成到您的应用中"}, "tips": {"title": "重要提示", "wechat": {"security": "请确保回调地址已在微信公众号后台正确配置，否则授权会失败", "redirect": "回调地址必须使用HTTPS协议（本地开发除外）", "scope": "snsapi_userinfo需要用户手动确认授权，snsapi_base为静默授权", "state": "建议使用state参数防止CSRF攻击，确保请求的安全性"}, "workWechat": {"security": "请确保回调地址已在企业微信管理后台正确配置，否则授权会失败", "redirect": "回调地址必须使用HTTPS协议（本地开发除外）", "scope": "snsapi_privateinfo需要用户手动确认授权，snsapi_base为静默授权", "state": "建议使用state参数防止CSRF攻击，确保请求的安全性", "agent": "请确保AgentID对应的应用已启用并在可见范围内"}}, "features": {"wechat": "微信公众号授权链接生成", "workWechat": "企业微信授权链接生成", "customization": "自定义授权参数", "preview": "实时预览和测试"}, "meta": {"title": "微信授权链接生成器 - 免费在线OAuth链接构建工具", "description": "免费的微信公众号和企业微信OAuth授权链接生成器，支持自定义参数配置，快速生成标准的微信授权链接。", "keywords": "微信授权,<PERSON><PERSON><PERSON>,公众号授权,企业微信,授权链接,微信开发,网页授权"}}