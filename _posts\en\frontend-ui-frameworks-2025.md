---
title: "The Great Frontend UI Framework Battle of 2025: Why ShadCN/UI is Crushing Ant Design"
excerpt: "From veteran Ant Design to rising star ShadCN/UI, the frontend UI framework landscape is changing fast. Which one will dominate 2025? Find out here."
date: "2025-07-21"
lastModified: "2025-07-21"
tags: ["Frontend Development", "UI Framework", "React", "Vue", "Component Library"]
author:
  name: "Lafu Code"
  avatar: "/images/authors/lafu.jpg"
featured: false
featuredOrder: 1
coverImage: "/assets/blog/26.png"
---

Recently, our company started a new project, and I spent a lot of time choosing the UI framework. From traditional Ant Design to the trendy ShadCN/UI, each has its own characteristics. As a frontend developer who has been working for several years, I'd like to share my experience.

## What are the mainstream UI frameworks now?

When it comes to UI frameworks, they can be roughly divided into several camps:

**Traditional Stable Camp**  
Ant Design is still the go-to choice for enterprise projects, Element Plus holds a solid position in the Vue ecosystem, and Material-UI follows Google's design guidelines.

**Modern Customization Camp**  
ShadCN/UI has been incredibly popular lately, Radix UI focuses on unstyled components, and Headless UI follows the same approach.

**Big Tech Products**  
ByteDance's Semi Design and Arco Design, plus other choices like Chakra UI.

## ShadCN/UI - The Recent Favorite

Honestly, ShadCN/UI is the framework I've been using the most lately. Its concept is interesting - instead of being a traditional npm package, it directly copies component code into your project.

```typescript
import { Button } from "@/components/ui/button";

// Since the code is in your project, you can modify it however you want
export function CustomButton() {
  return <Button className="bg-gradient-to-r from-purple-500 to-pink-500">Custom Button</Button>;
}
```

What are the benefits of this approach?

First, you have complete control over the components. Don't like a component's style? Just modify the source code directly. Second, it works particularly well with Tailwind CSS, making development enjoyable. Plus, with TypeScript support, the development experience is really nice.

However, there are downsides too, like the relatively limited number of components, and if your team's CSS skills are average, it might feel a bit complex.

**My recommendation: Suitable for small to medium projects, especially those with design requirements.**

## Radix UI - Maximum Freedom

Radix UI takes the opposite extreme - it only provides component behavior logic, leaving styling completely up to you. When I first encountered this concept, I thought it was strange, but after using it for a while, I found it really makes sense.

```typescript
import * as Dialog from "@radix-ui/react-dialog";

function CustomModal() {
  return (
    <Dialog.Root>
      <Dialog.Trigger className="px-4 py-2 bg-blue-500 text-white rounded">Open Dialog</Dialog.Trigger>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white p-6 rounded-lg">
          <h2>Custom Dialog</h2>
          <p>Styling completely controlled by you</p>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
```

Radix UI's strength lies in its excellent accessibility - keyboard navigation and screen reader support are handled for you. And since it only manages logic without styling, it can work with any CSS framework.

The downside is the relatively high learning curve, requiring you to be familiar with both HTML structure and CSS.

**My recommendation: Suitable for experienced teams with extremely high design requirements.**

## Semi Design - ByteDance's Enterprise Choice

Semi Design is ByteDance's open-source UI framework. I've used it in several projects, and overall it feels pretty good.

```typescript
import { Button, Table, ConfigProvider } from "@douyinfe/semi-ui";

function App() {
  return (
    <ConfigProvider theme="dark">
      <div>
        <Button type="primary">Semi Button</Button>
        <Table dataSource={data} columns={columns} />
      </div>
    </ConfigProvider>
  );
}
```

Semi Design has a relatively modern design language and comprehensive components. It performs particularly well when handling large datasets, with good performance optimization. The theme switching feature is also very practical.

However, compared to Ant Design, the ecosystem is still lacking, and you might have trouble finding solutions when encountering problems.

**My recommendation: Suitable for medium to large projects, especially scenarios with performance requirements.**

## Ant Design - Old but Still Strong

When it comes to enterprise UI frameworks, Ant Design is still unavoidable. Although it's criticized for having outdated design, you have to admit it's really stable.

```typescript
import { Button, Table, Form, Input } from "antd";

function MyComponent() {
  return (
    <div>
      <Form layout="vertical">
        <Form.Item label="Username" name="username">
          <Input placeholder="Please enter username" />
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit">
            Submit
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
}
```

Ant Design's advantages are obvious: comprehensive components, detailed documentation, active community, and complete ecosystem. Basically, for any business scenario you can think of, it has corresponding components. After years of development, most pitfalls have been filled.

The downside is that customization is relatively troublesome, and the bundle size is relatively large.

**My recommendation: First choice for enterprise projects, especially scenarios requiring high stability.**

## How to choose in actual projects?

Based on my experience, choosing a UI framework mainly depends on these factors:

### Project Scale

- **Large enterprise projects**: Ant Design, safe choice
- **Medium commercial projects**: Semi Design or ShadCN/UI
- **Small innovative projects**: ShadCN/UI or Radix UI

### Team Level

- **Senior teams**: Consider Radix UI + custom styles
- **Intermediate level**: Semi Design or ShadCN/UI
- **Beginner teams**: Ant Design, comprehensive documentation, easy to get started

### Design Requirements

- **High customization**: Radix UI or ShadCN/UI
- **Standardized design**: Ant Design or Semi Design
- **Rapid prototyping**: Ant Design

### Maintenance Cost

- **Long-term maintenance**: Choose those with complete ecosystems, like Ant Design
- **Short-term projects**: Can choose more flexible ones, like ShadCN/UI

## My Personal Recommendations

If I were to recommend for different scenarios:

**Beginners or time-pressed projects**: Just use Ant Design, don't overthink it. While it might not be perfect, it's definitely the safest choice.

**Some experience, want better design**: Try ShadCN/UI, used with Tailwind CSS, the development experience is quite nice.

**Strong technical team with extremely high design requirements**: Radix UI + custom style system. Although it requires more work, you can achieve the most suitable results.

**Large enterprise projects**: Still Ant Design, or consider Semi Design.

## Summary

There's no standard answer for choosing UI frameworks - the key is to combine your actual situation. Don't blindly pursue new technologies, and don't stick to old frameworks. The most important thing is to choose a solution that fits your project and team.

My personal advice is, if you're still struggling, just use Ant Design to get the project running first, then optimize slowly when you have time. After all, a working project is a good project.

Which UI frameworks have you used in your projects? Any pitfall experiences? Feel free to share in the comments!
