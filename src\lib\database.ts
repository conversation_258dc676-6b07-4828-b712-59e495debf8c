import { createClient, type Client } from '@libsql/client'

// 全局类型声明
declare global {
  var __libsql: Client | undefined
}

function createLibSQLClient(): Client {
  // 检查环境变量
  if (!process.env.TURSO_DATABASE_URL || !process.env.TURSO_AUTH_TOKEN) {
    // 在构建时如果没有环境变量，创建一个空的客户端
    if (process.env.NODE_ENV === 'production' || process.env.NEXT_PHASE === 'phase-production-build') {
      console.warn('数据库环境变量未设置，使用空客户端')
      return createClient({
        url: 'file:memory:',
        authToken: '',
      })
    }
    throw new Error('TURSO_DATABASE_URL 和 TURSO_AUTH_TOKEN 环境变量必须设置')
  }

  // 创建 LibSQL 客户端
  return createClient({
    url: process.env.TURSO_DATABASE_URL,
    authToken: process.env.TURSO_AUTH_TOKEN,
  })
}

// 使用全局变量避免热重载时创建多个实例
export const db = globalThis.__libsql ?? createLibSQLClient()

if (process.env.NODE_ENV !== 'production') {
  globalThis.__libsql = db
}

// 在模块加载时自动初始化数据库
import { initDatabase } from './db/init'

// 异步初始化数据库（不阻塞模块加载）
// 只在运行时初始化，构建时完全跳过
if (typeof window === 'undefined' &&
    process.env.NODE_ENV !== 'test' &&
    process.env.NEXT_PHASE !== 'phase-production-build' &&
    !process.env.BUILDING) {
  // 延迟初始化，避免阻塞模块加载
  setTimeout(() => {
    initDatabase().catch((error) => {
      console.warn('数据库初始化失败:', error.message)
    })
  }, 1000)
}
