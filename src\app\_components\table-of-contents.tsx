"use client";

import { useEffect, useState } from "react";
import { useTranslations } from 'next-intl';

interface TocItem {
  id: string;
  text: string;
  level: number;
}

interface Props {
  content: string;
}

export function TableOfContents({ content }: Props) {
  const t = useTranslations('components.tableOfContents');
  const [toc, setToc] = useState<TocItem[]>([]);
  const [activeId, setActiveId] = useState<string>("");
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    
    // 解析HTML内容，提取标题
    if (typeof window === 'undefined') return;

    // 创建临时 div 元素来解析 HTML，避免创建完整的 HTML 文档
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = content;
    const headings = tempDiv.querySelectorAll("h1, h2, h3, h4, h5, h6");

    const usedIds = new Set<string>();

    const tocItems: TocItem[] = Array.from(headings).map((heading, index) => {
      let id = heading.id;

      // 如果没有ID，生成一个
      if (!id && heading.textContent) {
        id = heading.textContent
          .toLowerCase()
          .replace(/[^\w\s-]/g, '') // 移除特殊字符
          .replace(/\s+/g, '-') // 空格替换为连字符
          .trim();
      }

      // 如果ID为空或者已经被使用，生成一个唯一的ID
      if (!id || usedIds.has(id)) {
        id = `heading-${index}`;
      }

      // 确保ID的唯一性
      let uniqueId = id;
      let counter = 1;
      while (usedIds.has(uniqueId)) {
        uniqueId = `${id}-${counter}`;
        counter++;
      }

      usedIds.add(uniqueId);

      return {
        id: uniqueId,
        text: heading.textContent || "",
        level: parseInt(heading.tagName.charAt(1)),
      };
    });

    setToc(tocItems);
  }, [content]);

  // 确保DOM中的标题有正确的ID
  useEffect(() => {
    if (toc.length === 0 || typeof document === 'undefined') return;

    const headings = document.querySelectorAll(".markdown h1, .markdown h2, .markdown h3, .markdown h4, .markdown h5, .markdown h6");

    headings.forEach((heading, index) => {
      const tocItem = toc[index];
      if (tocItem && !heading.id) {
        heading.id = tocItem.id;
      }
    });
  }, [toc]);

  useEffect(() => {
    if (typeof window === 'undefined' || typeof document === 'undefined') return;
    
    // 监听滚动，高亮当前标题
    const handleScroll = () => {
      const headings = document.querySelectorAll(".markdown h1, .markdown h2, .markdown h3, .markdown h4, .markdown h5, .markdown h6");
      let currentActiveId = "";

      // 找到当前视口中最接近顶部的标题
      let closestHeading: HTMLElement | null = null;
      let closestDistance = Infinity;

      headings.forEach((heading) => {
        const htmlHeading = heading as HTMLElement;
        const rect = htmlHeading.getBoundingClientRect();
        const distance = Math.abs(rect.top - 120); // 120px offset for navbar

        if (rect.top <= 120 && distance < closestDistance) {
          closestDistance = distance;
          closestHeading = htmlHeading;
        }
      });

      if (closestHeading && (closestHeading as HTMLElement).id) {
        currentActiveId = (closestHeading as HTMLElement).id;
      }

      setActiveId(currentActiveId);
    };

    // 初始调用
    handleScroll();

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, [toc]);

  const scrollToHeading = (id: string) => {
    if (typeof document === 'undefined' || typeof window === 'undefined') return;
    
    // 首先尝试通过ID查找
    let element = document.getElementById(id);

    // 如果没找到，尝试通过文本内容查找
    if (!element) {
      const headings = document.querySelectorAll(".markdown h1, .markdown h2, .markdown h3, .markdown h4, .markdown h5, .markdown h6");
      const tocItem = toc.find(item => item.id === id);

      if (tocItem) {
        element = Array.from(headings).find(heading =>
          heading.textContent?.trim() === tocItem.text.trim()
        ) as HTMLElement;
      }
    }

    if (element) {
      const navbarHeight = 120; // 导航栏高度
      const elementTop = element.offsetTop - navbarHeight;

      window.scrollTo({
        top: elementTop,
        behavior: "smooth",
      });

      // 更新活动状态
      setActiveId(id);
    } else {
      console.warn(`Element with id "${id}" not found`);
    }
  };

  const [isCollapsed, setIsCollapsed] = useState(false);

  // Don't render until mounted to prevent hydration mismatch
  if (!isMounted || toc.length === 0) return null;

  return (
    <div className="lg:sticky lg:top-24 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-lg sm:rounded-xl border border-slate-200/50 dark:border-slate-700/50 p-3 sm:p-4 lg:p-6 shadow-lg mb-6 lg:mb-0">
      <button
        onClick={() => setIsCollapsed(!isCollapsed)}
        className="w-full flex items-center justify-between text-base sm:text-lg font-semibold text-slate-900 dark:text-white mb-3 sm:mb-4 lg:cursor-default"
      >
        <div className="flex items-center gap-2">
          <svg className="w-4 h-4 sm:w-5 sm:h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
          </svg>
          {t('title')}
        </div>
        <svg 
          className={`w-4 h-4 transition-transform duration-200 lg:hidden ${
            isCollapsed ? 'rotate-180' : ''
          }`} 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
        </svg>
      </button>
      
      <nav className={`space-y-1 ${isCollapsed ? 'hidden lg:block' : 'block'}`}>
        {toc.map((item, index) => (
          <button
            key={`${item.id}-${index}`}
            onClick={() => {
              scrollToHeading(item.id);
              setIsCollapsed(true); // 移动端点击后收起目录
            }}
            className={`block w-full text-left py-1.5 sm:py-2 px-2 sm:px-3 rounded-md sm:rounded-lg text-xs sm:text-sm transition-all duration-200 hover:bg-blue-50 dark:hover:bg-blue-900/20 ${
              activeId === item.id
                ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 font-medium border-l-2 border-blue-500"
                : "text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-200"
            }`}
            style={{ paddingLeft: `${(item.level - 1) * 8 + 8}px` }}
          >
            <span className="truncate">{item.text}</span>
          </button>
        ))}
      </nav>
      
      {/* 进度指示器 - 仅在桌面端显示 */}
      <div className={`mt-4 sm:mt-6 pt-3 sm:pt-4 border-t border-slate-200 dark:border-slate-700 hidden lg:block ${isCollapsed ? 'lg:hidden' : ''}`}>
        <div className="flex items-center gap-2 text-xs text-slate-500 dark:text-slate-400">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{t('clickToJump')}</span>
        </div>
      </div>
    </div>
  );
}
