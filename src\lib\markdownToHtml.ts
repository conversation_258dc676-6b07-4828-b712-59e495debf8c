import { remark } from "remark";
import html from "remark-html";
import remarkGfm from "remark-gfm";

// 生成标题ID的函数
function generateHeadingId(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // 移除特殊字符
    .replace(/\s+/g, '-') // 空格替换为连字符
    .trim();
}

// 为HTML中的标题添加ID
function addHeadingIds(html: string): string {
  const usedIds = new Set<string>();
  let headingCounter = 0;

  return html.replace(/<h([1-6])>(.*?)<\/h[1-6]>/g, (match, level, content) => {
    const textContent = content.replace(/<[^>]*>/g, ''); // 移除HTML标签
    let id = generateHeadingId(textContent);

    // 如果ID为空或者已经被使用，生成一个唯一的ID
    if (!id || usedIds.has(id)) {
      id = `heading-${headingCounter}`;
    }

    // 确保ID的唯一性
    let uniqueId = id;
    let counter = 1;
    while (usedIds.has(uniqueId)) {
      uniqueId = `${id}-${counter}`;
      counter++;
    }

    usedIds.add(uniqueId);
    headingCounter++;

    return `<h${level} id="${uniqueId}">${content}</h${level}>`;
  });
}

// 处理mermaid代码块
function processMermaidBlocks(html: string): string {
  // 匹配mermaid代码块的正则表达式
  const mermaidRegex = /<pre><code class="language-mermaid">([\s\S]*?)<\/code><\/pre>/g;

  return html.replace(mermaidRegex, (match, code) => {
    // 解码HTML实体
    const decodedCode = code
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&amp;/g, '&')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'");

    // 生成唯一ID
    const chartId = `mermaid-ssr-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

    // 返回一个特殊的div，客户端会处理它
    return `<div class="mermaid-placeholder" data-mermaid-code="${encodeURIComponent(decodedCode)}" data-chart-id="${chartId}"></div>`;
  });
}

export default async function markdownToHtml(markdown: string) {
  const result = await remark()
    .use(remarkGfm) // GitHub Flavored Markdown support
    .use(html)
    .process(markdown);

  // 为标题添加ID
  let htmlWithIds = addHeadingIds(result.toString());

  // 处理mermaid代码块
  htmlWithIds = processMermaidBlocks(htmlWithIds);

  return htmlWithIds;
}
