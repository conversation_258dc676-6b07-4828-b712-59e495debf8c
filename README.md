# 老夫撸代码 - 技术博客

这是一个基于 Next.js 和 MDX 构建的现代化技术博客，专注于分享软件开发经验、编程技巧和技术洞察。

## 🚀 特性

- **现代化技术栈**: 使用 Next.js 15 + TypeScript + Tailwind CSS
- **MDX 支持**: 支持在 Markdown 中使用 React 组件
- **静态生成**: 利用 Next.js 的 SSG 功能，提供极佳的性能
- **响应式设计**: 完美适配桌面端和移动端
- **深色模式**: 内置主题切换功能
- **SEO 优化**: 自动生成 meta 标签和 Open Graph 信息
- **类型安全**: 完整的 TypeScript 支持

## 📝 博客内容

目前包含以下技术文章：

- **现代前端开发：从 React 到 Next.js 的演进之路** - 探索前端技术的发展历程
- **编写优雅代码：软件开发中的最佳实践** - 分享代码质量和开发规范
- **技术选型的艺术：如何为项目选择合适的技术栈** - 技术决策的思考框架

## 🛠️ 技术栈

- **框架**: Next.js 15 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **内容**: MDX (Markdown + React)
- **部署**: Vercel (推荐)

## 🚀 快速开始

### 安装依赖

```bash
npm install
```

### 环境变量配置

复制环境变量示例文件并配置：

```bash
cp .env.example .env.local
```

编辑 `.env.local` 文件，配置以下变量：

```bash
# Google Analytics (可选)
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# 数据库配置 (Turso/LibSQL)
TURSO_DATABASE_URL=libsql://your-database-url.turso.io
TURSO_AUTH_TOKEN=your-auth-token
```

### 启动开发服务器

```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看博客。

### 构建生产版本

```bash
npm run build
npm start
```

## 📁 项目结构

```
├── _posts/                 # 博客文章 (Markdown 文件)
├── public/                 # 静态资源
├── src/
│   ├── app/               # Next.js App Router
│   │   ├── _components/   # React 组件
│   │   ├── posts/         # 文章页面
│   │   └── globals.css    # 全局样式
│   ├── interfaces/        # TypeScript 接口定义
│   └── lib/              # 工具函数
├── package.json
└── README.md
```

## ✍️ 添加新文章

1. 在 `_posts` 目录下创建新的 `.md` 文件
2. 添加 Front Matter 元数据：

```markdown
---
title: "文章标题"
excerpt: "文章摘要"
coverImage: "/assets/blog/cover.jpg"
date: "2024-06-30T10:00:00.000Z"
author:
  name: "作者名称"
  picture: "/assets/blog/authors/author.jpeg"
ogImage:
  url: "/assets/blog/cover.jpg"
---

文章内容...
```

3. 保存文件，Next.js 会自动生成对应的页面路由

## 🎨 自定义

### 修改网站信息

编辑 `src/lib/constants.ts` 文件来修改网站的基本信息。

### 修改样式

项目使用 Tailwind CSS，你可以：

- 修改 `tailwind.config.ts` 来自定义主题
- 在 `src/app/globals.css` 中添加全局样式
- 在组件中使用 Tailwind 类名

### 添加新功能

- 在 `src/app/_components/` 中添加新组件
- 在 `src/lib/` 中添加工具函数
- 在 `src/interfaces/` 中定义 TypeScript 类型

## 📦 部署

### Vercel (推荐)

1. 将代码推送到 GitHub
2. 在 [Vercel](https://vercel.com) 中导入项目
3. 自动部署完成

### 其他平台

项目支持部署到任何支持 Node.js 的平台：

```bash
npm run build
npm start
```

## 📈 Google Analytics 集成

本项目已集成 Google Analytics (GA4) 用于网站分析。

### 配置 Google Analytics

1. **创建 GA4 属性**：

   - 访问 [Google Analytics](https://analytics.google.com/)
   - 创建新的 GA4 属性
   - 获取 Measurement ID (格式：G-XXXXXXXXXX)

2. **配置环境变量**：

   ```bash
   # 在 .env.local 中添加
   NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
   ```

3. **功能特性**：

   - ✅ 页面浏览跟踪
   - ✅ 搜索事件跟踪
   - ✅ 联系表单提交跟踪
   - ✅ 微信公众号点击跟踪
   - ✅ 外部链接点击跟踪
   - ✅ 只在生产环境启用
   - ✅ 隐私保护 (IP 匿名化)

4. **事件跟踪**：
   - `page_view` - 页面浏览
   - `search` - 搜索操作
   - `form_submit` - 表单提交
   - `click_external_link` - 外部链接点击
   - `wechat_qr_click` - 微信二维码点击

### 开发环境

在开发环境中，GA 脚本不会加载，避免污染分析数据。只有在生产环境 (`NODE_ENV=production`) 且配置了有效的 GA ID 时才会启用。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## 📄 许可证

MIT License

---

**老夫撸代码** - 分享有价值的编程经验和技术洞察

---

\_Author: pythonsir
