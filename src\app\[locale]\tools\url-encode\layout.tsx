import { Metadata } from "next";
import { getTranslations } from 'next-intl/server';
import { SITE_NAME, SITE_URL } from "@/lib/constants";

type Props = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.tools.tools.urlEncode' });
  const tSite = await getTranslations({ locale, namespace: 'site' });

  const getLocalizedPath = (path: string) => {
    if (locale === 'zh') {
      return path;
    }
    return `/${locale}${path}`;
  };

  const title = `${t('meta.title')} | ${tSite('name')}`;
  const description = t('meta.description');
  const canonicalPath = getLocalizedPath('/tools/url-encode');

  return {
    title,
    description,
    keywords: locale === 'zh' ? [
      "URL编码",
      "URL解码",
      "URL转换",
      "encodeURIComponent",
      "encodeURI",
      "中文URL编码",
      "特殊字符编码",
      "开发工具",
      "前端工具",
      "免费工具"
    ] : [
      "URL encoding",
      "URL decoding",
      "URL converter",
      "encodeURIComponent",
      "encodeURI",
      "URL encoder",
      "special characters encoding",
      "developer tools",
      "frontend tools",
      "free tools"
    ],
    authors: [{ name: tSite('author') }],
    creator: tSite('author'),
    publisher: tSite('name'),
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL(SITE_URL),
    alternates: {
      canonical: canonicalPath,
      languages: {
        'zh': '/tools/url-encode',
        'en': '/en/tools/url-encode',
      },
    },
    openGraph: {
      title,
      description,
      url: canonicalPath,
      siteName: tSite('name'),
      locale: locale === 'zh' ? 'zh_CN' : 'en_US',
      type: "website",
      images: [
        {
          url: "/images/tools/url-encode-og.jpg",
          width: 1200,
          height: 630,
          alt: t('meta.title'),
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: ["/images/tools/url-encode-og.jpg"],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  };
}

export default function UrlEncodeLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}