const createNextIntlPlugin = require('next-intl/plugin');

const withNextIntl = createNextIntlPlugin('./src/i18n.ts');

/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  // 优化外部脚本加载
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          // 允许 Google AdSense 相关域名
          {
            key: 'Content-Security-Policy',
            value: "frame-src 'self' https://googleads.g.doubleclick.net https://tpc.googlesyndication.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://pagead2.googlesyndication.com https://www.googletagmanager.com https://googleads.g.doubleclick.net; connect-src 'self' https://pagead2.googlesyndication.com https://www.google-analytics.com https://cm.g.doubleclick.net;",
          },
        ],
      },
    ];
  },
};

module.exports = withNextIntl(nextConfig);