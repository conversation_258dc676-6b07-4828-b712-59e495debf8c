---
featured: false
featuredOrder: 1
featuredReason: "Revealing the phenomenon of 'fake programmers' in the workplace who earn high salaries without writing code."
title: "Shocking! These 'Fake Programmers' Earn More Than You But Never Write Code..."
excerpt: "Last night I was working overtime until 11 PM when my colleague <PERSON> told me a secret: that new architect in our team earns $120K annually, but I've never seen him write a single line of code..."
coverImage: "/assets/blog/38.png"
date: "2025-08-01"
lastModified: "2025-08-01"
author:
  name: "LaFu Code"
  picture: "/images/avatar1.jpg"
tags:
  - Workplace Observations
  - Programmers
  - Career Development
  - Soft Skills
  - Technical Management
---

# Shocking! These 'Fake Programmers' Earn More Than You But Never Write Code...

Last night I was working overtime until 11 PM, just about to pack up and go home, when my colleague <PERSON> came over and whispered: "<PERSON><PERSON>, you know that new architect in our team? I heard he earns $120K annually, but I've never seen him write a single line of code..."

I was stunned for a moment, thinking about my observations over the past few months—it was indeed true. This guy is either in meetings or drawing diagrams every day, makes PowerPoints better than product managers, but ask him to look at a bug? "This problem is quite complex, you guys research it first and let me know the results."

Honestly, I initially thought this was an isolated case. But after working in this industry for seven or eight years and switching between three companies, I found there are quite a few people like this. Some people code until they go bald and earn $30K monthly; others never touch keyboards but can get $75K+.

Today let's talk about these "magical" colleagues and see if you have similar people around you?

> Disclaimer: This article is purely for fun. Any resemblance to real people is purely coincidental. But if you think it's describing someone you know... that might really be a coincidence 😏

---

## 1. **PowerPoint Engineers** - Starting at $45K Monthly

> When programming skills aren't enough, diagrams make up for it. IDE is PowerPoint, debugging relies on eloquence, deployment depends on smooth talking.

**The oddball I've seen:**
At my previous company, there was an architect surnamed Li. Everyone privately called him "PowerPoint Li." This guy indeed had some skills, but not in writing code—in making PowerPoints.

I remember when we were refactoring the user system, he spent two weeks on the proposal. When he presented it, we were all amazed—30 pages of PowerPoint with all kinds of flashy architecture diagrams, talking about "domain-driven," "event sourcing," "CQRS patterns," sounding incredibly sophisticated.

The result? The project dragged on for half a year, and in the end, we regular developers built a simple version ourselves and launched it. But when Brother Li switched jobs, his resume said "Led the microservice architecture refactoring of a ten-million-user system"...

**Typical quotes:**
- "I did something similar at Alibaba before"
- "You implement it according to my ideas first, we'll talk if there are problems"
- "This architecture has high availability, high concurrency, and high scalability"
- "We need to use domain-driven design thinking"

**Income source:** Packaging concepts and pie-in-the-sky abilities, building presence at various tech sharing sessions, resume packaging that sounds amazing when job-hopping.

---

## 2. **Eternal Refactoring Perfectionist** - $37K+ Monthly

> Every time they see code they wrote last week, they want to tear it down and start over. Pursuing "architectural beauty," but products never go live.

**Example around me:**
We have a guy in our team with solid technical foundation, but he has one problem—he always thinks code isn't perfect enough.

Last year when working on an order module, what should have been a week's work took him two months. First week he said he needed to refactor with DDD, second week he said he needed to introduce event-driven architecture, third week he thought the database design wasn't elegant enough...

The funniest part was when the product manager urged him countless times, he always said "give me a few more days, let me optimize the architecture." Finally, the project manager couldn't stand it anymore and directly assigned a fresh graduate who wrote the simplest CRUD following the prototype and launched it in two days.

But this guy is quite active in tech groups, often sharing profound architecture articles, and everyone thinks he's awesome.

**Typical quotes:**
- "I'm not satisfied with this design, let me optimize it"
- "I'm planning to split this service into 8 microservices"
- "Code quality is more important than delivery time"
- "This doesn't comply with SOLID principles"

**Income source:** Writes good technical documentation, has some influence in tech communities, often headhunted.

---

## 3. **"Keyboard Warrior" Architect** - $52K+ Monthly

> Points out everything during code reviews but never gets hands dirty. First to shift blame when problems arise.

**Personal experience:**
Our current company has a technical director with deep coding foundation, but he just doesn't like hands-on work. Every code review, his comments can fill a page, from variable naming to exception handling, from design patterns to performance optimization, pickier than a perfectionist.

Once I submitted a feature, and he left me 15 comments. I thought I was doomed and would have to make major changes. After reading them, I found every comment made sense, but the problem was—why don't you tell me how to fix it!

Discuss specific implementation with him? "You guys think about this, I believe in your abilities." Online problem occurs? First to jump out in the group: "I said there were risks here long ago, why didn't you listen?"

Honestly, this person does have skills, but it's a love-hate relationship.

**Typical quotes:**
- "This logic isn't elegant enough, suggest refactoring"
- "Why not use factory pattern for implementation?"
- "I said there were risks here before"
- "You misunderstood what I meant"

**Income source:** Solid technical foundation, excellent interview performance, always gets high-salary offers when job-hopping.

---

## 4. **Copy-Paste Master** - $30K+ Monthly

> If you can Ctrl+C, never write it yourself. Stack Overflow is their second IDE.

**The genius I've seen:**
I had a colleague before, frontend developer, incredibly efficient. Others took a day to write a component, he finished in half a day. I admired him at first, then discovered his secret...

This guy's browser bookmarks had GitHub, Stack Overflow, CodePen lined up. After receiving requirements, the first thing wasn't analysis, but searching for "similar wheels." Found one, copy it over, change the styles, adjust parameters, done.

The most ridiculous time, he copied a date picker with a variable called "todayIsMonday," but the actual functionality had nothing to do with what day of the week it was. I asked why he didn't change the name, he said: "If it works, why change it? What if changing it breaks something?"

Later there was a bug online where users occasionally got errors when selecting dates. After investigating for ages, we found the copied code had an unhandled edge case, and this bug was already mentioned in the comments under the original Stack Overflow answer...

**Typical quotes:**
- "There are ready-made solutions online, why reinvent the wheel?"
- "I just borrowed the idea"
- "This bug should be a third-party library issue"
- "Let me check if there are better solutions"

**Income source:** Fast development speed, can deliver features quickly, very popular in companies that pursue efficiency.

---

## 5. **Meeting-Type Programmer** - $42K+ Monthly

> No time to write code, schedule packed with meetings. After meetings end, they organize meetings to summarize the meetings.

**The legend in our team:**
Our Tech Lead is a meeting maniac, his calendar is fuller than the CEO's. Once I sneaked a peek at his schedule, from 9 AM to 7 PM, densely packed with meetings.

The funniest part is he can package any problem as "needs meeting discussion." Last time I asked him about an API interface issue, which could have been explained in two WeChat messages, but he said: "This problem is quite complex, let's pull a meeting to align."

Then he actually scheduled a 1-hour meeting with attendees including frontend, backend, testing, product... After discussing for 50 minutes, the conclusion was: "Just follow what's written in the documentation."

Even more amazing, after the meeting he'd send an email summary, then schedule another meeting next week to review the execution of this email. I suspect he gets commission based on the number of meetings...

**Typical quotes:**
- "We need to align on this"
- "This problem is complex, let's have a meeting to discuss"
- "I suggest everyone bring up their pain points"
- "I'll send meeting minutes to everyone later"

**Income source:** Strong communication skills, plays important role in cross-department collaboration, beloved by management.

---

## 6. **New Addition: Open Source God** - $60K+ Monthly

> Tens of thousands of stars on GitHub, but writes average code for company projects.

**The master I met:**
Last year a new colleague joined with a resume that blinded us—a GitHub project with 20K stars, speaker at various tech conferences, columnist on tech platforms. HR was thrilled, thinking they'd struck gold.

After joining, we found this guy's business code writing was... how to put it, just average. His open source project was indeed awesome, but it was all flashy demos, far from production-ready.

The funniest part was he always complained that company business code "lacks technical content" and "isn't elegant enough." Brother, we're here to make money, not create art, okay?

But honestly, the company really buys into this. Every external promotion mentions "we have well-known contributors from the open source community," instantly boosting the company's prestige.

**Typical quotes:**
- "I implemented it this way in my open source project"
- "I shared this technology at a certain conference"
- "Business code indeed isn't as challenging as open source projects"

---

## 7. **New Addition: AI Era Prompt Engineer** - $33K+ Monthly

> ChatGPT is their main development tool, they write prompts more fluently than code.

**The prodigy of the new era:**
Recently an intern born in 2000 joined our team, introducing himself as an "AI-native programmer." I was confused—what's AI-native?

Later I discovered this kid's coding method was indeed "native"—entirely relying on ChatGPT. After receiving requirements, he'd spend half an hour carefully designing prompts, then let GPT generate code, copy-paste, debug, done.

Efficiency was indeed high, but problems were numerous. Once GPT generated code with an obvious memory leak, he couldn't see it at all, and said "AI-generated code definitely has no problems."

Most ridiculous was he actually gave an internal company presentation titled "How to Become a Prompt Engineer," with quite professional PowerPoint. After listening, I fell into deep thought: Did I waste all those years learning programming?

**Typical quotes:**
- "I'll have GPT implement this requirement for me"
- "In the AI era, asking questions matters, not programming"
- "Traditional programming thinking is outdated"

---

## Who's Feeding These "Fake Programmers"?

Seeing this, you might ask: Why can these people get high salaries? What makes them thrive in the workplace?

### 1. **Information Asymmetry**

Many managers without technical backgrounds cannot accurately assess the real level of technical personnel. They're more easily impressed by beautifully packaged PowerPoints, fluent technical jargon, and impressive resumes.

### 2. **Short-term Oriented Assessment Mechanisms**

Many companies' performance evaluations favor short-term results rather than long-term code quality and technical debt. People who can deliver features quickly are often more popular than those who write elegant code.

### 3. **Delayed Nature of Technical Debt**

Problems with poor code and architecture design often only surface months or even years later. By then, the person who wrote the code might have already switched jobs.

### 4. **Importance of Soft Skills**

In modern software development, communication, coordination, and packaging abilities are sometimes more important than pure technical skills. These "fake programmers" often excel in soft skills.

### 5. **Market Supply and Demand**

The hot technical talent market has led many companies to lower hiring standards. As long as you can complete basic work, they're willing to offer decent salaries.

---

## How to Identify "Fake Programmers" Around You?

### Technical Level Identification Methods:

1. **Check code commit records**: Real programmers have stable code commits
2. **Ask about specific implementation details**: Fake programmers often only know concepts, not specific implementations
3. **Observe problem-solving approaches**: Independent thinking vs. relying on search engines
4. **Look at reasons for technology choices**: Can they explain the trade-offs behind technical decisions

### Behavioral Level Identification Methods:

1. **Reaction when encountering problems**: Proactive solving vs. shifting responsibility
2. **Attitude toward new technologies**: Rational learning vs. blind trend-following
3. **Team collaboration performance**: Really helping the team vs. only caring about personal performance

---

## Advice for Real Programmers

If you're a programmer who writes code diligently and see these "fake programmers" doing better than you, you might feel unfair. But remember:

### 1. **Technology is Fundamental, But Not Everything**

- While maintaining technical abilities, also improve soft skills
- Learn to package and showcase your technical achievements
- Proactively take on more responsibilities, show leadership

### 2. **Build Personal Brand**

- Be active in tech communities, share your experiences
- Write tech blogs, document your thinking process
- Participate in open source projects, showcase your coding abilities

### 3. **Choose the Right Company**

- Look for companies that truly value technology
- Avoid teams that only focus on short-term results
- Find leaders who can recognize your value

### 4. **Continuous Learning and Growth**

- Don't give up on improving yourself because of "fake programmers"
- Real technical ability is a long-term competitive advantage
- The market will ultimately reward truly valuable people

---

## Final Words

After saying all this, I'm not trying to bash anyone. Every industry has all kinds of people, and the programming industry is no exception. Some people make a living from technology, others from other skills—there's nothing inherently right or wrong about that.

It's just that sometimes when you see colleagues who are technically average but doing better than you, you inevitably feel a bit unbalanced. But thinking carefully, if they can get high salaries, they must have their own strengths, even if not in technical aspects.

**The main point of writing this article is:**

If you're a programmer who writes code diligently, don't get too depressed seeing these "magical" colleagues. Technology is fundamental, but not everything. Learning some of their "soft skills" appropriately, like communication, packaging, and coordination abilities, can also help career development.

Of course, if you happen to be one of the types mentioned in the article... all I can say is, know when to stop, don't go too far 😅

**Do you have similar "magical" colleagues around you? Welcome to share your stories in the comments!**

---

_Disclaimer: This article is purely for entertainment. If you find yourself fitting the description, that might really be a coincidence. We're all working people, let's understand each other and progress together._
