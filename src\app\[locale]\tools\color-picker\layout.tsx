import { Metadata } from "next";
import { getTranslations } from 'next-intl/server';
import { SITE_NAME, SITE_URL } from "@/lib/constants";

type Props = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.tools.tools.colorPicker' });
  const tSite = await getTranslations({ locale, namespace: 'site' });

  const getLocalizedPath = (path: string) => {
    if (locale === 'zh') {
      return path;
    }
    return `/${locale}${path}`;
  };

  const getHreflangAlternates = (basePath: string) => {
    return {
      'zh': basePath,
      'en': `/en${basePath}`,
    };
  };

  const title = `${t('meta.title')} | ${tSite('name')}`;
  const description = t('meta.description');
  const canonicalPath = getLocalizedPath('/tools/color-picker');

  return {
    title,
    description,
    keywords: locale === 'zh' ? [
      "颜色拾取器",
      "颜色选择器",
      "调色板",
      "颜色转换",
      "RGB转换",
      "HEX颜色",
      "HSL颜色",
      "HSV颜色",
      "颜色代码",
      "设计工具",
      "前端工具",
      "UI设计",
      "颜色搭配",
      "渐变生成器",
      "在线调色板"
    ] : [
      "color picker",
      "color selector",
      "color palette",
      "color converter",
      "RGB converter",
      "HEX color",
      "HSL color",
      "HSV color",
      "color codes",
      "design tools",
      "frontend tools",
      "UI design",
      "color harmony",
      "gradient generator",
      "online palette"
    ],
    authors: [{ name: tSite('author') }],
    creator: tSite('author'),
    publisher: tSite('name'),
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL(SITE_URL),
    alternates: {
      canonical: canonicalPath,
      languages: getHreflangAlternates('/tools/color-picker'),
    },
    openGraph: {
      title,
      description,
      url: canonicalPath,
      siteName: tSite('name'),
      locale: locale === 'zh' ? 'zh_CN' : 'en_US',
      type: "website",
      images: [
        {
          url: "/images/tools/color-picker-og.jpg",
          width: 1200,
          height: 630,
          alt: t('meta.title'),
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: ["/images/tools/color-picker-og.jpg"],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  };
}

export default function ColorPickerLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}