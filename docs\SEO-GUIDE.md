# 文章SEO优化指南

## 概述

每篇文章现在都有独立的SEO配置，不再依赖网站全局SEO设置。这确保了每篇文章都能获得最佳的搜索引擎优化效果。

## 文章Front Matter配置

在每篇文章的Markdown文件头部，您可以配置以下SEO相关字段：

```markdown
---
title: "文章标题"
excerpt: "文章摘要，用于meta description"
coverImage: "/assets/blog/image.jpg"
date: "2025-07-06"
lastModified: "2025-07-06"  # 可选，最后修改时间
author:
  name: "作者名称"
  picture: "/assets/blog/authors/author.jpeg"
---
```

## 自动生成的SEO元素

### 1. 基础Meta标签
- `title`: 文章标题 | 网站名称
- `description`: 使用文章的excerpt字段
- `keywords`: 从文章标题自动提取关键词
- `author`: 使用文章作者信息
- `canonical`: 文章的规范URL

### 2. Open Graph标签
- `og:type`: "article"
- `og:title`: 文章标题
- `og:description`: 文章摘要
- `og:url`: 文章完整URL
- `og:image`: 文章封面图片
- `og:site_name`: 网站名称
- `article:published_time`: 发布时间
- `article:modified_time`: 修改时间
- `article:author`: 作者信息
- `article:section`: "Technology"
- `article:tag`: 自动提取的标签

### 3. Twitter Cards
- `twitter:card`: "summary_large_image"
- `twitter:title`: 文章标题
- `twitter:description`: 文章摘要
- `twitter:image`: 文章封面图片
- `twitter:creator`: 作者信息

### 4. 结构化数据 (JSON-LD)
自动生成符合Schema.org标准的文章结构化数据：

```json
{
  "@context": "https://schema.org",
  "@type": "BlogPosting",
  "headline": "文章标题",
  "description": "文章摘要",
  "author": {
    "@type": "Person",
    "name": "作者名称",
    "url": "网站URL"
  },
  "publisher": {
    "@type": "Organization",
    "name": "网站名称",
    "url": "网站URL"
  },
  "datePublished": "发布时间",
  "dateModified": "修改时间",
  "image": "封面图片URL",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "文章URL"
  }
}
```

## 最佳实践

### 1. 文章标题
- 长度控制在50-60个字符
- 包含主要关键词
- 具有吸引力和描述性

### 2. 文章摘要 (excerpt)
- 长度控制在150-160个字符
- 准确描述文章内容
- 包含相关关键词
- 具有吸引点击的能力

### 3. 封面图片
- 推荐尺寸：1200x630像素
- 文件大小控制在500KB以内
- 使用高质量、相关的图片
- 确保图片有意义的alt文本

### 4. 发布和修改时间
- 使用ISO 8601格式：`YYYY-MM-DD`
- 重大更新时更新`lastModified`字段
- 保持时间信息的准确性

### 5. 作者信息
- 使用真实的作者姓名
- 保持作者信息的一致性
- 可以链接到作者的个人页面

## 关键词优化

系统会自动从文章标题中提取关键词，但您也可以通过以下方式优化：

1. **标题优化**: 在标题中包含主要关键词
2. **内容结构**: 使用清晰的标题层次结构
3. **内部链接**: 适当添加相关文章的内部链接
4. **外部链接**: 链接到权威的外部资源

## 技术实现

SEO优化通过以下文件实现：

- `src/app/posts/[slug]/page.tsx`: 主要的metadata生成逻辑
- `src/app/_components/structured-data.tsx`: 结构化数据组件
- `src/interfaces/post.ts`: 文章类型定义
- `src/lib/constants.ts`: 网站基础配置

## 验证和测试

建议使用以下工具验证SEO效果：

1. **Google Search Console**: 监控搜索表现
2. **Facebook Sharing Debugger**: 测试Open Graph标签
3. **Twitter Card Validator**: 验证Twitter Cards
4. **Google Rich Results Test**: 测试结构化数据
5. **PageSpeed Insights**: 检查页面性能

## 注意事项

1. 确保所有图片路径正确且可访问
2. 定期检查和更新过时的内容
3. 保持URL结构的稳定性
4. 避免重复的meta描述
5. 确保移动端友好性

通过遵循这些指南，每篇文章都能获得最佳的SEO效果，提高在搜索引擎中的可见性和排名。
