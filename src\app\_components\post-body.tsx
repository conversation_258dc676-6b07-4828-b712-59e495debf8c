import markdownStyles from "./markdown-styles.module.css";
import { TableOfContents } from "./table-of-contents";
import { MermaidInit } from "./mermaid-init";

type Props = {
  content: string;
};

export function PostBody({ content }: Props) {
  return (
    <div className="relative overflow-hidden">
      <div className="grid lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8">
        {/* 主要内容 */}
        <div className="lg:col-span-3 order-2 lg:order-1 min-w-0">
          <div className="max-w-none overflow-hidden">
            <div
              className={`${markdownStyles["markdown"]} overflow-hidden`}
              dangerouslySetInnerHTML={{ __html: content }}
            />
            {/* Mermaid初始化 */}
            <MermaidInit />
          </div>
        </div>

        {/* 目录侧边栏 - 移动端显示在内容上方 */}
        <div className="lg:col-span-1 order-1 lg:order-2 min-w-0">
          <div className="lg:sticky lg:top-24">
            <TableOfContents content={content} />
          </div>
        </div>
      </div>
    </div>
  );
}
