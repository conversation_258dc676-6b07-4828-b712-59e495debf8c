"use client";

import { useTranslations } from 'next-intl';
import { calculateReadingTime, formatReadingTime } from '@/lib/reading-time';

interface Props {
  content: string;
  format?: 'full' | 'short' | 'minimal';
  className?: string;
}

export function ReadingTime({ content, format = 'full', className }: Props) {
  const t = useTranslations('common.readingTimeFormats');

  const minutes = calculateReadingTime(content);

  // 使用 next-intl 的参数传递功能
  const readingTimeText = t(format, { minutes });

  return (
    <span className={className}>
      {readingTimeText}
    </span>
  );
}
