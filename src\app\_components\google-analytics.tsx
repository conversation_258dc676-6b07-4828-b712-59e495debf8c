"use client";

import Script from 'next/script';
import { GA_TRACKING_ID, isGAEnabled } from '@/lib/gtag';

export function GoogleAnalytics() {
  // 如果没有启用 GA 或没有 tracking ID，不渲染任何内容
  if (!isGAEnabled) {
    return null;
  }

  return (
    <>
      {/* Google Analytics gtag.js */}
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`}
      />
      <Script
        id="google-analytics"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${GA_TRACKING_ID}', {
              page_location: window.location.href,
              page_title: document.title,
              // 隐私设置
              anonymize_ip: true,
              // 性能设置
              send_page_view: true,
              // 自定义配置
              custom_map: {
                'custom_parameter_1': 'article_category',
                'custom_parameter_2': 'reading_time'
              }
            });
          `,
        }}
      />
    </>
  );
}
