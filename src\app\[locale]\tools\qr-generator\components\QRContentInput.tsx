import { useTranslations } from 'next-intl';

interface QRContentInputProps {
  qrType: string;
  textContent: string;
  setTextContent: (content: string) => void;
  urlContent: string;
  setUrlContent: (content: string) => void;
  wifiConfig: {
    ssid: string;
    password: string;
    security: string;
    hidden: boolean;
  };
  setWifiConfig: (config: {
    ssid: string;
    password: string;
    security: string;
    hidden: boolean;
  }) => void;
  vcardInfo: {
    name: string;
    phone: string;
    email: string;
    company: string;
    title: string;
    website: string;
  };
  setVcardInfo: (info: {
    name: string;
    phone: string;
    email: string;
    company: string;
    title: string;
    website: string;
  }) => void;
}

export default function QRContentInput({
  qrType,
  textContent,
  setTextContent,
  urlContent,
  setUrlContent,
  wifiConfig,
  setWifiConfig,
  vcardInfo,
  setVcardInfo
}: QRContentInputProps) {
  const t = useTranslations('pages.tools.tools.qrGenerator');

  return (
    <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6">
      <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
        {t('content.title')}
      </h2>

      {qrType === 'text' && (
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('content.text')}
          </label>
          <textarea
            value={textContent}
            onChange={(e) => setTextContent(e.target.value)}
            placeholder={t('content.textPlaceholder')}
            className="w-full px-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-slate-700 text-gray-900 dark:text-white resize-none"
            rows={4}
          />
        </div>
      )}

      {qrType === 'url' && (
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('content.url')}
          </label>
          <input
            type="url"
            value={urlContent}
            onChange={(e) => setUrlContent(e.target.value)}
            placeholder={t('content.urlPlaceholder')}
            className="w-full px-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
          />
        </div>
      )}

      {qrType === 'wifi' && (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('wifi.ssid')}
            </label>
            <input
              type="text"
              value={wifiConfig.ssid}
              onChange={(e) => setWifiConfig({ ...wifiConfig, ssid: e.target.value })}
              placeholder="WiFi网络名称"
              className="w-full px-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('wifi.password')}
            </label>
            <input
              type="password"
              value={wifiConfig.password}
              onChange={(e) => setWifiConfig({ ...wifiConfig, password: e.target.value })}
              placeholder="WiFi密码"
              className="w-full px-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              加密方式
            </label>
            <select
              value={wifiConfig.security}
              onChange={(e) => setWifiConfig({ ...wifiConfig, security: e.target.value })}
              className="w-full px-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
            >
              <option value="WPA">WPA/WPA2</option>
              <option value="WEP">WEP</option>
              <option value="nopass">无密码</option>
            </select>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="hidden"
              checked={wifiConfig.hidden}
              onChange={(e) => setWifiConfig({ ...wifiConfig, hidden: e.target.checked })}
              className="mr-2"
            />
            <label htmlFor="hidden" className="text-sm text-gray-700 dark:text-gray-300">
              隐藏网络
            </label>
          </div>
        </div>
      )}

      {qrType === 'vcard' && (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              姓名
            </label>
            <input
              type="text"
              value={vcardInfo.name}
              onChange={(e) => setVcardInfo({ ...vcardInfo, name: e.target.value })}
              placeholder="张三"
              className="w-full px-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              电话号码
            </label>
            <input
              type="tel"
              value={vcardInfo.phone}
              onChange={(e) => setVcardInfo({ ...vcardInfo, phone: e.target.value })}
              placeholder="13800138000"
              className="w-full px-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              邮箱地址
            </label>
            <input
              type="email"
              value={vcardInfo.email}
              onChange={(e) => setVcardInfo({ ...vcardInfo, email: e.target.value })}
              placeholder="<EMAIL>"
              className="w-full px-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              公司名称
            </label>
            <input
              type="text"
              value={vcardInfo.company}
              onChange={(e) => setVcardInfo({ ...vcardInfo, company: e.target.value })}
              placeholder="公司名称"
              className="w-full px-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              职位
            </label>
            <input
              type="text"
              value={vcardInfo.title}
              onChange={(e) => setVcardInfo({ ...vcardInfo, title: e.target.value })}
              placeholder="职位"
              className="w-full px-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              网站
            </label>
            <input
              type="url"
              value={vcardInfo.website}
              onChange={(e) => setVcardInfo({ ...vcardInfo, website: e.target.value })}
              placeholder="https://example.com"
              className="w-full px-4 py-3 border border-gray-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
            />
          </div>
        </div>
      )}
    </div>
  );
}