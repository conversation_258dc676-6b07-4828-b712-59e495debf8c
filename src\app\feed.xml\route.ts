import { getAllPosts } from '@/lib/api'
import { SITE_URL } from '@/lib/constants'
import { getTranslations } from 'next-intl/server'
import { defaultLocale } from '@/i18n'

export async function GET() {
  const locale = defaultLocale
  const posts = getAllPosts(locale)
  const t = await getTranslations({ locale, namespace: 'site' })

  // 生成文章RSS项目
  const postItems = posts.map((post) => {
    const postUrl = `${SITE_URL}/posts/${post.slug}`
    const pubDate = new Date(post.date).toUTCString()

    return `
    <item>
      <title><![CDATA[${post.title}]]></title>
      <description><![CDATA[${post.excerpt || ''}]]></description>
      <link>${postUrl}</link>
      <guid isPermaLink="true">${postUrl}</guid>
      <pubDate>${pubDate}</pubDate>
      <category>文章</category>
    </item>`
  }).join('')

  const rssXml = `<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>${t('name')} - ${t('description')}</title>
    <description>${t('description')}</description>
    <link>${SITE_URL}</link>
    <language>zh-CN</language>
    <lastBuildDate>${new Date().toUTCString()}</lastBuildDate>
    <atom:link href="${SITE_URL}/feed.xml" rel="self" type="application/rss+xml" />
    ${postItems}
  </channel>
</rss>`

  return new Response(rssXml, {
    headers: {
      'Content-Type': 'application/rss+xml; charset=utf-8',
      'Cache-Control': 'public, max-age=3600, s-maxage=3600',
    },
  })
}
