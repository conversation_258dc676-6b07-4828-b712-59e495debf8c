{"title": "⏱️ Cron表达式解析工具", "description": "在线解析和验证cron表达式，查看执行时间规律和下次运行时间", "breadcrumb": {"home": "首页", "tools": "工具箱", "current": "Cron表达式解析"}, "input": {"title": "Cron表达式输入", "description": "输入标准的5字段cron表达式：分钟 小时 日 月 星期", "placeholder": "例如：0 9 * * 1-5", "label": "Cron表达式"}, "fields": {"names": ["分钟", "小时", "日", "月", "星期"], "ranges": ["0-59", "0-23", "1-31", "1-12", "0-7 (0和7都表示周日)"]}, "result": {"title": "解析结果", "fieldsTitle": "字段解析", "patternTitle": "执行规律", "nextRunsTitle": "预计执行时间（示例）", "description": "表达式说明", "nextRuns": "下次执行时间"}, "examples": {"title": "常用示例", "description": "点击示例快速填入", "list": [{"expression": "0 0 * * *", "description": "每天午夜执行"}, {"expression": "0 9 * * 1-5", "description": "工作日上午9点执行"}, {"expression": "*/15 * * * *", "description": "每15分钟执行一次"}, {"expression": "0 0 1 * *", "description": "每月1号午夜执行"}, {"expression": "0 0 * * 0", "description": "每周日午夜执行"}, {"expression": "30 2 * * *", "description": "每天凌晨2:30执行"}, {"expression": "0 */6 * * *", "description": "每6小时执行一次"}, {"expression": "0 0 1 1 *", "description": "每年1月1日执行"}]}, "syntax": {"title": "语法说明", "format": "格式：", "formatValue": "分钟 小时 日期 月份 星期", "specialChars": "特殊字符：", "chars": {"asterisk": "任意值", "question": "不指定值（仅日期和星期）", "dash": "范围（如 1-5）", "comma": "列表（如 1,3,5）", "slash": "步长（如 */5）"}}, "actions": {"parse": "解析", "clear": "清空", "copy": "复制", "copyTitle": "复制"}, "errors": {"invalidFields": "Cron表达式必须包含5个字段：分钟 小时 日 月 星期", "invalidFormat": "表达式格式错误，请检查各字段的值范围", "copyFailed": "复制失败", "noMatches": "未来7天内无匹配的执行时间"}, "weekdays": ["日", "一", "二", "三", "四", "五", "六", "日"], "timeDescriptions": {"executeTime": "执行时间：", "everyMinute": "每分钟", "everyNMinutes": "每{n}分钟", "minuteN": "第{n}分钟", "everyHour": "的每小时", "everyNHours": "，每{n}小时", "hourN": "，{n}点", "dayAndWeek": "，每月{day}号或每周{weekday}", "dayN": "，每月{day}号", "weekN": "，每周{weekday}", "monthN": "，{month}月", "anyValue": "任意值"}, "status": {"valid": "有效", "invalid": "无效"}, "meta": {"title": "Cron表达式解析器 - 在线Cron解析工具", "description": "免费的在线Cron表达式解析工具，支持可视化时间表和下次执行时间预测。"}}