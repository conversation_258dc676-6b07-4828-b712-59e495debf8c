{"title": "🤖 AI Git 提交信息生成器", "description": "智能生成规范的Git提交信息，支持多种AI服务和本地生成", "breadcrumb": {"home": "首页", "tools": "工具箱", "current": "AI Git 提交信息生成器"}, "features": {"title": "功能特点", "aiGeneration": "AI 智能生成", "localGeneration": "本地规则生成", "multiProvider": "多AI提供商支持", "oneClick": "一键操作", "smartFallback": "智能回退机制", "multiLanguage": "多语言支持", "multiStyle": "多种提交风格", "privacy": "隐私保护", "free": "完全免费", "offline": "离线可用"}, "providers": {"title": "支持的AI服务", "openai": "OpenAI (GPT-3.5/GPT-4)", "claude": "Anthropic <PERSON>", "gemini": "Google Gemini", "tongyi": "阿里云通义千问", "local": "本地规则生成"}, "usage": {"title": "使用方法", "step1": "在Git仓库中修改代码", "step2": "运行 git add . 暂存更改", "step3": "打开VS Code源码管理面板", "step4": "点击✨按钮生成提交信息", "step5": "检查并提交代码"}, "installation": {"title": "安装方式", "marketplace": "VS Code扩展市场搜索", "searchTerm": "LaFu AI Git Commit", "github": "GitHub开源项目", "website": "官方网站"}, "install": {"button": "安装扩展"}, "source": {"button": "查看源码"}, "cta": {"title": "🚀 立即开始使用", "description": "体验 AI 驱动的智能提交信息生成，提升你的开发效率！", "button": "免费安装扩展"}, "meta": {"title": "AI Git 提交信息生成器 - VS Code扩展插件", "description": "智能生成规范的Git提交信息的VS Code扩展，支持OpenAI、<PERSON>、Gemini、通义千问等多种AI服务，提供本地生成和智能回退机制，完全免费使用。", "keywords": "Git提交信息,<PERSON>生成,VS Code扩展,<PERSON><PERSON><PERSON>,<PERSON>,<PERSON>,通义千问,开发工具,代码提交,自动化"}}