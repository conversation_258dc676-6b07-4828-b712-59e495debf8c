{"title": "🆔 身份证验证工具", "description": "在线验证中国身份证号码的有效性，支持15位和18位身份证", "breadcrumb": {"home": "首页", "tools": "工具箱", "current": "身份证验证"}, "input": {"label": "身份证号码", "placeholder": "请输入身份证号码..."}, "result": {"title": "验证结果", "valid": "有效", "invalid": "无效", "info": {"region": "归属地区", "birthday": "出生日期", "gender": "性别", "age": "年龄", "constellation": "星座"}, "gender": {"male": "男", "female": "女"}}, "actions": {"validate": "验证", "clear": "清空", "copy": "复制信息"}, "messages": {"copySuccess": "信息已复制到剪贴板！", "emptyInput": "请输入身份证号码", "invalidFormat": "身份证号码格式不正确", "validCard": "身份证号码有效", "invalidCard": "身份证号码无效", "lengthError": "身份证号码必须为18位", "regionError": "地区代码不存在", "dateError": "出生日期不存在", "futureDateError": "出生日期不能大于当前日期", "yearError": "出生年份不能小于1900年", "checksumError": "校验码不正确", "validationPassed": "验证通过", "validating": "正在验证身份证号码...", "validationFailed": "验证失败", "copied": "已复制", "copyResult": "复制结果", "copyFailed": "复制失败", "inputLength": "已输入", "unknown": "未知"}, "constellations": {"aquarius": "水瓶座", "pisces": "双鱼座", "aries": "白羊座", "taurus": "金牛座", "gemini": "双子座", "cancer": "巨蟹座", "leo": "狮子座", "virgo": "处女座", "libra": "天秤座", "scorpio": "天蝎座", "sagittarius": "射手座", "capricorn": "摩羯座"}, "zodiac": {"monkey": "猴年", "rooster": "鸡年", "dog": "狗年", "pig": "猪年", "rat": "鼠年", "ox": "牛年", "tiger": "虎年", "rabbit": "兔年", "dragon": "龙年", "snake": "蛇年", "horse": "马年", "goat": "羊年"}, "ui": {"home": "首页", "toolbox": "工具箱", "toolIntro": "工具介绍", "inputArea": "输入区域", "validationStatus": "验证状态", "validationResult": "验证结果", "detailInfo": "详细信息", "usageInstructions": "使用说明", "region": "归属地区", "birthDate": "出生日期", "gender": "性别", "age": "年龄", "constellation": "星座", "zodiac": "生肖", "male": "男", "female": "女", "years": "岁", "dateFormat": "年{month}月{day}日"}, "features": {"title": "功能特点", "format": "格式验证", "checksum": "校验码验证", "info": "信息解析", "privacy": "本地验证"}, "instructions": {"title": "使用说明", "support18": "支持验证中国大陆18位身份证号码", "autoparse": "自动解析归属地区、出生日期、年龄、性别等信息", "checksum": "包含校验码验证，确保号码的准确性", "astrology": "提供星座和生肖信息", "privacy": "所有验证均在本地进行，不会上传任何数据"}, "meta": {"title": "身份证验证工具 - 在线身份证号码验证", "description": "免费的在线身份证验证工具，支持15位和18位身份证号码验证，本地处理保护隐私。"}}