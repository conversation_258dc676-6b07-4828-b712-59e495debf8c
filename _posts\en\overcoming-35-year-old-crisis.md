---
excerpt: "Just turned 35 this year, and quite a few friends around me are anxious about the so-called '35-year-old crisis'. Sharing my thoughts and practices over the years on how to find your value and positioning at this age."
coverImage: "/assets/blog/9.png"
author:
  name: Lafu Code
  picture: "/assets/blog/authors/tim.jpeg"
featured: false
title: "I'm 35 Now, and I'm Not Anxious Anymore"
date: "2025-07-01"
lastModified: "2025-07-01"
---

Just turned 35 this year, and on my birthday, a bunch of people in my social media were joking about the "35-year-old crisis."

To be honest, I was anxious about it for the past two years. Watching young colleagues code all night without breaking a sweat, learning new technologies with insatiable hunger, while I started worrying about keeping up physically and being eliminated.

But after two years of thinking and practice, I've realized that the so-called "35-year-old crisis" is more psychological panic. The key is finding your value and positioning at this age.

## Where My Anxiety Came From

### Physical Stamina Really Isn't What It Used to Be

I used to pull all-nighters for a week straight, now one night and I'm wasted the next day. Watching those 23, 24-year-old guys full of energy definitely creates a sense of crisis.

### Learning New Technologies Has Slowed Down

It's not that I can't learn, but I learn slower than before. I used to pick up a new framework over a weekend, now I need more time to understand and digest.

### Family Responsibilities Have Increased

After having kids, I can't invest all my time into work like before. Weekends are for family, evenings for homework help - much less disposable time.

### Impact from Young People

New graduates with newer tech stacks, lower salary requirements, stronger learning abilities. Sometimes I really wonder: what advantages do I still have?

## How I Adjusted My Mindset

### Accept Reality, Don't Fight It Head-On

At 35, don't compete with 25-year-olds on stamina and learning speed. It's like making a marathon runner compete with sprinters in the 100m - pointless.

My strategy now: **Use experience and judgment to compensate for lack of stamina and speed**.

### Redefine My Own Value

When young, my value was "can do work", now my value is "can solve problems."

**Before**: Product manager says build a feature, I just dive into coding.
**Now**: Product manager says build a feature, I first ask why we need it, if there are better solutions, what are the costs and risks of technical implementation.

This thinking ability is something young people don't have yet.

## What I've Done These Years

### From Technical Expert to Problem Solver

I no longer pursue mastering all the latest technologies, but focus on solving business problems.

**Example**: Last year our company's order system had frequent issues, young colleagues always wanted to refactor with the latest tech. I spent a week analyzing the existing system, found the root cause was poor database design, and solved 80% of the problems by optimizing a few key queries.

This "leverage" ability is my advantage at this age.

### From Executor to Decision Maker

I started participating more in technical decisions, not just execution.

- When choosing tech stack, I consider team's technical level, project timeline, maintenance costs
- When designing architecture, I consider system scalability and maintainability, not just functionality
- When facing problems, I think from business perspective, not just technical

### From Individual Contributor to Team Enabler

I discovered that helping the team grow is more valuable than writing code myself.

- I proactively share experience, helping young colleagues avoid pitfalls I've encountered
- During code reviews, I don't just point out problems, but explain why certain approaches are bad
- During project retrospectives, I summarize lessons learned and form team best practices

## Some Specific Advice

### Build Your Own Moat

**Depth is more important than breadth**. Rather than knowing a little about everything, become expert-level in one area.

I chose to specialize in backend architecture. Now when the team encounters complex system design problems, they come to me for discussion. This irreplaceability is my moat.

### Develop Soft Skills

Technology becomes obsolete, but communication skills, project management abilities, and business understanding don't.

I've deliberately practiced these past two years:

- **Communication skills**: Learning to explain technical problems in language non-technical people can understand
- **Project management**: Learning to assess risks, make plans, coordinate resources
- **Business understanding**: Deeply understanding the company's business model and processes

### Build Personal Brand

I started writing technical blogs, not to show off, but to summarize and share.

Through blogging, I found my understanding of technology deepened and my expression improved. More importantly, it gave me some recognition in the industry.

### Keep Learning, But Be Selective

I no longer chase all new technologies, but learn selectively.

**Learning principles**:

- Prioritize learning technologies that solve current business problems
- Focus on technology essence and principles, not just syntax and APIs
- Learn technologies with long-term value, not fleeting trends

## Advantages of Being 35

Looking back, being 35 actually has many advantages:

### Rich Experience

I've seen all kinds of pitfalls, know which solutions work and which have risks. This judgment is something young people need time to accumulate.

### Stable Mindset

Won't get anxious over one technical problem, won't doubt life over one failure. This stability is important for teams.

### Broader Vision

I don't just focus on technology, but also on business, users, and commercial value. This global perspective helps me make better decisions.

### Strong Communication Skills

I can communicate effectively with people from different backgrounds, explain complex technical problems clearly. This is important in cross-departmental collaboration.

## Final Thoughts

35 isn't an endpoint, but a new starting point.

The key is recognizing your position, leveraging your advantages, rather than competing head-on with young people on stamina and learning speed.

Our value at this age isn't in how many lines of code we've written, but how many problems we've solved, how many people we've helped, how much value we've created.

If you're also anxious about the "35-year-old crisis," take a moment to think: your experience, your judgment, your communication skills - these are advantages young people don't have yet.

Use these advantages well, and 35-year-old programmers can still be very valuable.
