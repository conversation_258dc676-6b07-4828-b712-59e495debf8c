import { getTranslations } from 'next-intl/server';
import { SITE_NAME, SITE_URL } from "@/lib/constants";
import PrivacyPageClient from './PrivacyPageClient';

type Props = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.privacy' });
  const tSite = await getTranslations({ locale, namespace: 'site' });

  return {
    title: `${t('title')} - ${tSite('name')}`,
    description: t('description'),
    keywords: locale === 'zh' ? 
      ["隐私政策", "数据保护", "个人信息", "用户隐私", "数据安全", "法律条款"] :
      ["privacy policy", "data protection", "personal information", "user privacy", "data security", "legal terms"],
    authors: [{ name: tSite('name') }],
    metadataBase: new URL(SITE_URL),
    alternates: {
      canonical: locale === 'en' ? '/en/privacy' : '/privacy',
      languages: {
        'zh': '/privacy',
        'en': '/en/privacy',
      },
    },
    openGraph: {
      type: "website",
      locale: locale === 'zh' ? "zh_CN" : "en_US",
      title: `${t('title')} - ${tSite('name')}`,
      description: t('description'),
      siteName: tSite('name'),
      url: locale === 'en' ? '/en/privacy' : '/privacy',
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

export default async function PrivacyPage({ params }: Props) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.privacy' });
  const tSite = await getTranslations({ locale, namespace: 'site' });

  // Pre-fetch all translation data
  const translations = {
    title: t('title'),
    lastUpdated: t('lastUpdated'),
    sections: {
      informationCollection: {
        title: t('sections.informationCollection.title'),
        description: t('sections.informationCollection.description'),
        items: {
          email: t('sections.informationCollection.items.email'),
          access: t('sections.informationCollection.items.access'),
          usage: t('sections.informationCollection.items.usage')
        }
      },
      informationUsage: {
        title: t('sections.informationUsage.title'),
        description: t('sections.informationUsage.description'),
        items: [
          t('sections.informationUsage.items.0'),
          t('sections.informationUsage.items.1'),
          t('sections.informationUsage.items.2'),
          t('sections.informationUsage.items.3')
        ]
      },
      informationProtection: {
        title: t('sections.informationProtection.title'),
        description: t('sections.informationProtection.description'),
        items: [
          t('sections.informationProtection.items.0'),
          t('sections.informationProtection.items.1'),
          t('sections.informationProtection.items.2'),
          t('sections.informationProtection.items.3')
        ]
      },
      cookies: {
        title: t('sections.cookies.title'),
        description: t('sections.cookies.description'),
        items: [
          t('sections.cookies.items.0'),
          t('sections.cookies.items.1'),
          t('sections.cookies.items.2')
        ],
        control: t('sections.cookies.control')
      },
      userRights: {
        title: t('sections.userRights.title'),
        description: t('sections.userRights.description'),
        items: [
          t('sections.userRights.items.0'),
          t('sections.userRights.items.1'),
          t('sections.userRights.items.2'),
          t('sections.userRights.items.3')
        ]
      },
      advertising: {
        title: t('sections.advertising.title'),
        description: t('sections.advertising.description'),
        items: [
          t('sections.advertising.items.0'),
          t('sections.advertising.items.1'),
          t('sections.advertising.items.2'),
          t('sections.advertising.items.3')
        ],
        important: t('sections.advertising.important'),
        importantItems: [
          t('sections.advertising.importantItems.0'),
          t('sections.advertising.importantItems.1'),
          t('sections.advertising.importantItems.2'),
          t('sections.advertising.importantItems.3')
        ]
      },
      thirdParty: {
        title: t('sections.thirdParty.title'),
        description: t('sections.thirdParty.description'),
        items: [
          t('sections.thirdParty.items.0'),
          t('sections.thirdParty.items.1'),
          t('sections.thirdParty.items.2'),
          t('sections.thirdParty.items.3')
        ],
        recommendation: t('sections.thirdParty.recommendation')
      },
      policyUpdates: {
        title: t('sections.policyUpdates.title'),
        description: t('sections.policyUpdates.description')
      },
      contact: {
        title: t('sections.contact.title'),
        description: t('sections.contact.description'),
        items: {
          email: t('sections.contact.items.email'),
          website: t('sections.contact.items.website')
        }
      }
    },
    summary: t('summary'),
    siteName: tSite('name')
  };

  return <PrivacyPageClient locale={locale} translations={translations} />;
}


