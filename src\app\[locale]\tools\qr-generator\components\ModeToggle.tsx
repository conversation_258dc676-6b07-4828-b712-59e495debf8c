import { useTranslations } from 'next-intl';

interface ModeToggleProps {
  batchMode: boolean;
  setBatchMode: (mode: boolean) => void;
}

export default function ModeToggle({ batchMode, setBatchMode }: ModeToggleProps) {
  const t = useTranslations('pages.tools.tools.qrGenerator');
  return (
    <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">
          {t('modes.title')}
        </h2>
        <div className="flex bg-gray-100 dark:bg-slate-700 rounded-lg p-1">
          <button
            onClick={() => setBatchMode(false)}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              !batchMode 
                ? 'bg-white dark:bg-slate-600 text-gray-900 dark:text-white shadow-sm' 
                : 'text-gray-600 dark:text-gray-400'
            }`}
          >
            {t('modes.single')}
          </button>
          <button
            onClick={() => setBatchMode(true)}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              batchMode 
                ? 'bg-white dark:bg-slate-600 text-gray-900 dark:text-white shadow-sm' 
                : 'text-gray-600 dark:text-gray-400'
            }`}
          >
            {t('modes.batch')}
          </button>
        </div>
      </div>
    </div>
  );
}