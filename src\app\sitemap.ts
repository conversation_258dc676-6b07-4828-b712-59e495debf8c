import { MetadataRoute } from 'next'
import { getAllPosts } from '@/lib/api'
import { getAllTools } from '@/lib/tools-config'
import { SITE_URL } from '@/lib/constants'
import { locales, defaultLocale } from '@/i18n'

export default function sitemap(): MetadataRoute.Sitemap {
  // 为每种语言生成静态页面
  const staticPages = locales.flatMap(locale => {
    const localePrefix = locale === defaultLocale ? '' : `/${locale}`
    return [
      {
        url: `${SITE_URL}${localePrefix}`,
        lastModified: new Date(),
        changeFrequency: 'daily' as const,
        priority: 1,
      },
      {
        url: `${SITE_URL}${localePrefix}/about`,
        lastModified: new Date(),
        changeFrequency: 'monthly' as const,
        priority: 0.8,
      },
      {
        url: `${SITE_URL}${localePrefix}/posts`,
        lastModified: new Date(),
        changeFrequency: 'daily' as const,
        priority: 0.9,
      },
      {
        url: `${SITE_URL}${localePrefix}/tools`,
        lastModified: new Date(),
        changeFrequency: 'weekly' as const,
        priority: 0.9,
      },
      {
        url: `${SITE_URL}${localePrefix}/privacy`,
        lastModified: new Date(),
        changeFrequency: 'yearly' as const,
        priority: 0.3,
      },
      {
        url: `${SITE_URL}${localePrefix}/terms`,
        lastModified: new Date(),
        changeFrequency: 'yearly' as const,
        priority: 0.3,
      },
      {
        url: `${SITE_URL}${localePrefix}/ads-policy`,
        lastModified: new Date(),
        changeFrequency: 'yearly' as const,
        priority: 0.3,
      },

    ]
  })

  // 动态获取工具页面
  const tools = getAllTools()
  const toolPages = locales.flatMap(locale => {
    const localePrefix = locale === defaultLocale ? '' : `/${locale}`
    return tools.map(tool => ({
      url: `${SITE_URL}${localePrefix}${tool.href}`,
      lastModified: new Date(tool.publishDate),
      changeFrequency: 'monthly' as const,
      priority: 0.7,
    }))
  })

  // 为每种语言生成文章页面
  const postPages = locales.flatMap(locale => {
    const localePrefix = locale === defaultLocale ? '' : `/${locale}`
    const posts = getAllPosts(locale)
    return posts.map((post) => ({
      url: `${SITE_URL}${localePrefix}/posts/${post.slug}`,
      lastModified: new Date(post.lastModified || post.date),
      changeFrequency: 'weekly' as const,
      priority: post.featured ? 0.9 : 0.8, // 精选文章优先级更高
    }))
  })

  // RSS Feeds - 根据最新文章时间设置lastModified
  const latestPostDate = locales.reduce((latest, locale) => {
    const posts = getAllPosts(locale)
    if (posts.length > 0) {
      const postDate = new Date(posts[0].date)
      return postDate > latest ? postDate : latest
    }
    return latest
  }, new Date(0))

  const feedPages = [
    {
      url: `${SITE_URL}/feed.xml`,
      lastModified: latestPostDate,
      changeFrequency: 'daily' as const,
      priority: 0.9, // RSS feed优先级提高
    },
    {
      url: `${SITE_URL}/en/feed.xml`,
      lastModified: latestPostDate,
      changeFrequency: 'daily' as const,
      priority: 0.9, // RSS feed优先级提高
    }
  ]

  return [...staticPages, ...toolPages, ...postPages, ...feedPages]
}
