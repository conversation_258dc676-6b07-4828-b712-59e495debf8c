import { type Author } from "@/interfaces/author";
import Link from "next/link";
import Image from "next/image";
import CoverImage from "./cover-image";
import DateFormatter from "./date-formatter";

type Props = {
  title: string;
  coverImage: string;
  date: string;
  excerpt: string;
  author: Author;
  slug: string;
};

export function PostPreview({
  title,
  coverImage,
  date,
  excerpt,
  author,
  slug,
}: Props) {
  return (
    <article className="card-hover group overflow-hidden">
      {/* 封面图片 */}
      <div className="relative overflow-hidden">
        <div className="aspect-video">
          <CoverImage slug={slug} title={title} src={coverImage} />
        </div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        {/* 悬浮阅读按钮 - 使用3D效果增强立体感 */}
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-4 group-hover:translate-y-0">
          <Link
            href={`/posts/${slug}`}
            className="btn-3d"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            阅读文章
          </Link>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="p-6">
        {/* 日期标签 */}
        <div className="flex items-center gap-2 mb-4">
          <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
          <span className="text-sm text-neutral-500 dark:text-neutral-400 font-medium">
            <DateFormatter dateString={date} />
          </span>
        </div>

        {/* 标题 */}
        <h3 className="text-xl md:text-2xl font-bold leading-tight mb-3 text-neutral-900 dark:text-neutral-100">
          <Link
            href={`/posts/${slug}`}
            className="hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300"
          >
            {title}
          </Link>
        </h3>

        {/* 摘要 */}
        <p className="text-neutral-600 dark:text-neutral-300 leading-relaxed mb-6 line-clamp-3">
          {excerpt}
        </p>

        {/* 底部信息 */}
        <div className="flex items-center justify-between">
          {/* 作者信息 */}
          <div className="flex items-center gap-3">
            <div className="relative w-8 h-8">
              <Image
                src="/images/avatar1.jpg"
                alt={author.name}
                fill
                className="rounded-full object-cover"
              />
            </div>
            <div>
              <p className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                {author.name}
              </p>
            </div>
          </div>

          {/* 阅读更多链接 */}
          <Link
            href={`/posts/${slug}`}
            className="btn-gradient-border text-sm group/link"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 0l3 3m-3-3l3-3" />
            </svg>
            阅读更多
            <svg className="w-4 h-4 transform group-hover/link:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </div>
      </div>

      {/* 底部装饰线 */}
      <div className="h-1 bg-gradient-to-r from-primary-500 to-secondary-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
    </article>
  );
}
