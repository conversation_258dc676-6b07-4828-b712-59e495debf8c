{"title": "🔗 URL编码解码工具", "description": "在线URL编码和解码工具，支持中文和特殊字符处理", "breadcrumb": {"home": "首页", "tools": "工具箱", "current": "URL编码解码"}, "controls": {"mode": "模式", "encode": "编码", "decode": "解码", "encodeType": "编码类型", "swap": "交换", "clear": "清空"}, "sections": {"originalUrl": "原始URL", "encodedUrl": "编码URL", "encodeResult": "编码结果", "decodeResult": "解码结果", "characters": "字符", "copy": "复制"}, "placeholders": {"encodeInput": "请输入要编码的URL或文本...", "decodeInput": "请输入要解码的编码URL...", "encodeOutput": "编码结果将显示在这里...", "decodeOutput": "解码结果将显示在这里..."}, "errors": {"processFailed": "处理失败，请检查输入内容"}, "messages": {"copied": "已复制到剪贴板！"}, "tips": {"title": "使用说明", "encodeTypes": {"title": "编码类型", "items": {"0": "<strong>encodeURIComponent</strong>: 编码所有特殊字符，适用于URL参数", "1": "<strong>encodeURI</strong>: 只编码非ASCII字符，保留URL结构"}}, "commonUses": {"title": "常见用途", "items": {"0": "编码URL中的中文字符和特殊符号", "1": "处理包含空格和特殊字符的URL参数", "2": "解码编码后的URL以查看原始内容"}}}, "meta": {"title": "URL编码解码工具 - 在线URL编码器", "description": "免费的在线URL编码解码工具，支持中文和特殊字符，操作简单，结果准确。"}}