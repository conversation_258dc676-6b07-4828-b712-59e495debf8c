import { HOME_OG_IMAGE_URL, SITE_NAME, SITE_DESCRIPTION, SITE_AUTHOR } from "@/lib/constants";
import type { Metadata } from "next";
import { getMessages, getTranslations } from 'next-intl/server';
import { Inter } from "next/font/google";
import cn from "classnames";
import { GoogleAnalytics } from "./_components/google-analytics";


import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

// 生成元数据
export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations({ locale: 'zh', namespace: 'site' });

  return {
    title: `${t('name')} - ${t('description')}`,
    description: t('description'),
    openGraph: {
      images: [HOME_OG_IMAGE_URL],
      title: `${t('name')} - ${t('description')}`,
      description: t('description'),
      type: "website" as const,
    },
    keywords: t('keywords'),
    authors: [{ name: t('author') }],
  };
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // 获取中文翻译消息
  const messages = await getMessages({ locale: 'zh' });

  return (
    <html lang="zh">
      <head>
        <link
          rel="apple-touch-icon"
          sizes="180x180"
          href="/favicon/apple-touch-icon.png"
        />
        <link
          rel="icon"
          type="image/png"
          sizes="32x32"
          href="/favicon/favicon-32x32.png"
        />
        <link
          rel="icon"
          type="image/png"
          sizes="16x16"
          href="/favicon/favicon-16x16.png"
        />
        <link rel="manifest" href="/favicon/site.webmanifest" />
        <link
          rel="mask-icon"
          href="/favicon/safari-pinned-tab.svg"
          color="#000000"
        />
        <link rel="shortcut icon" href="/favicon/favicon.ico" />
        <meta name="msapplication-TileColor" content="#000000" />
        <meta
          name="msapplication-config"
          content="/favicon/browserconfig.xml"
        />
        <meta name="theme-color" content="#000" />
        <meta name="google-adsense-account" content="ca-pub-****************" />
        <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
             crossOrigin="anonymous"></script>
        <link rel="alternate" type="application/rss+xml" href="/feed.xml" />
      </head>
      <body className={cn(inter.className, "bg-white text-gray-900")}>
        {/* Google Analytics */}
        <GoogleAnalytics />

        {children}
      </body>
    </html>
  );
}
