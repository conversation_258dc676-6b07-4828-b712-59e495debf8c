{"title": "⏰ Timestamp Converter Tool", "description": "Online timestamp converter tool, supports bidirectional conversion between Unix timestamp and datetime", "breadcrumb": {"home": "Home", "tools": "Toolbox", "current": "Timestamp Converter"}, "modes": {"toTimestamp": "Date to Timestamp", "toDate": "Timestamp to Date"}, "input": {"timestamp": {"label": "Timestamp", "placeholder": "Enter Unix timestamp, e.g.: 1640995200"}, "datetime": {"label": "Date Time", "placeholder": "Select date and time"}}, "output": {"timestamp": "Timestamp Result", "datetime": "Date Time Result", "formats": {"title": "Multiple Formats", "iso": "ISO Format", "local": "Local Format", "utc": "UTC Format", "relative": "Relative Time"}}, "current": {"title": "Current Time", "timestamp": "Current Timestamp", "datetime": "Current Date Time"}, "currentTime": {"title": "Current Time", "dateTime": "Current Date Time", "timestampSeconds": "Timestamp (Seconds)", "timestampMilliseconds": "Timestamp (Milliseconds)"}, "controls": {"formatLabel": "Timestamp Format", "seconds": "Seconds", "milliseconds": "Milliseconds", "timestampToDate": "Timestamp to Date", "dateToTimestamp": "Date to Timestamp", "useCurrentTime": "Use Current Time", "clear": "Clear"}, "sections": {"timestamp": "Timestamp", "dateTime": "Date Time", "copy": "Copy"}, "placeholders": {"timestampSeconds": "Enter timestamp in seconds, e.g.: 1640995200", "timestampMilliseconds": "Enter timestamp in milliseconds, e.g.: 1640995200000", "dateTime": "Enter date time, e.g.: 2024-01-01 12:00:00"}, "descriptions": {"timestampSeconds": "Unix timestamp (seconds) - seconds since January 1, 1970", "timestampMilliseconds": "Unix timestamp (milliseconds) - milliseconds since January 1, 1970", "dateTimeFormats": "Supports multiple date formats: YYYY-MM-DD HH:mm:ss or MM/DD/YYYY"}, "tips": {"title": "Usage Tips", "timestampFormats": {"title": "Timestamp Formats", "items": {"0": "Seconds timestamp: 10 digits, e.g. <code>1640995200</code>", "1": "Milliseconds timestamp: 13 digits, e.g. <code>1640995200000</code>"}}, "dateFormats": {"title": "Date Formats", "items": {"0": "Standard format: 2024-01-01 12:00:00", "1": "US format: 01/01/2024 12:00:00 PM", "2": "ISO format: 2024-01-01T12:00:00.000Z", "3": "Relative time: supports natural language input"}}}, "actions": {"convert": "Convert", "clear": "Clear", "copy": "Copy", "getCurrentTime": "Get Current Time"}, "messages": {"copySuccess": "Result copied to clipboard!", "copyFailed": "Copy failed, please copy manually", "invalidTimestamp": "Invalid timestamp format", "invalidDate": "Invalid date format", "invalidDateTime": "Invalid date time format", "timestampCopied": "Timestamp copied to clipboard!", "dateTimeCopied": "Date time copied to clipboard!"}, "features": {"title": "Features", "bidirectional": "Bidirectional conversion", "formats": "Multiple time formats", "realtime": "Real-time conversion", "current": "Current time display", "copy": "One-click copy result"}, "instructions": {"title": "Instructions", "step1": "Select conversion mode: date to timestamp or timestamp to date", "step2": "Enter timestamp to convert or select date time", "step3": "View conversion results in multiple formats", "step4": "Copy result in required format"}, "meta": {"title": "Timestamp Converter Tool - Unix Timestamp and Date Conversion", "description": "Free online timestamp converter tool, supports bidirectional conversion between Unix timestamp and datetime, multiple time format display."}}