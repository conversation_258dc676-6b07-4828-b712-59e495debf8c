import { Metadata } from "next";
import { getTranslations } from 'next-intl/server';
import { SITE_NAME, SITE_URL } from "@/lib/constants";

type Props = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.tools.tools.ipLookup' });
  const tSite = await getTranslations({ locale, namespace: 'site' });

  const getLocalizedPath = (path: string) => {
    if (locale === 'zh') {
      return path;
    }
    return `/${locale}${path}`;
  };

  const getHreflangAlternates = (basePath: string) => {
    return {
      'zh': basePath,
      'en': `/en${basePath}`,
    };
  };

  const title = `${t('meta.title')} | ${tSite('name')}`;
  const description = t('meta.description');
  const canonicalPath = getLocalizedPath('/tools/ip-lookup');

  return {
    title,
    description,
    keywords: locale === 'zh' ? [
      "IP查询",
      "IP地址查询",
      "IP归属地查询",
      "IP定位",
      "IP地理位置",
      "ISP查询",
      "本机IP查询",
      "IPv4查询",
      "IPv6查询",
      "IP工具",
      "网络工具",
      "免费IP查询",
      "在线IP工具"
    ] : [
      "IP lookup",
      "IP address lookup",
      "IP geolocation",
      "IP location",
      "ISP lookup",
      "IP tools",
      "network tools",
      "free IP lookup",
      "online IP tools",
      "IPv4 lookup",
      "IPv6 lookup"
    ],
    authors: [{ name: tSite('author') }],
    creator: tSite('author'),
    publisher: tSite('name'),
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL(SITE_URL),
    alternates: {
      canonical: canonicalPath,
      languages: getHreflangAlternates('/tools/ip-lookup'),
    },
    openGraph: {
      title,
      description,
      url: canonicalPath,
      siteName: tSite('name'),
      locale: locale === 'zh' ? 'zh_CN' : 'en_US',
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      creator: "@laofucode",
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  };
}

export default function IPLookupLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}