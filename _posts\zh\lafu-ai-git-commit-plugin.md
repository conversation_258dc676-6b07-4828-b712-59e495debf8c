---
featured: false
featuredOrder: 1
title: "被 GitHub Copilot 的额度搞烦了，我自己撸了个 Git 提交信息生成插件"
excerpt: "GitHub Copilot 生成提交信息要消耗额度？我花几个周末时间，开发了一个完全免费的 Git 提交信息生成插件，支持本地生成和多种 AI 服务，永远不会失败的智能提交助手。"
date: "2025-07-24"
author:
  name: "老夫撸代码"
coverImage: "/assets/blog/28.png"
---

# 被 GitHub Copilot 的额度搞烦了，我自己撸了个 Git 提交信息生成插件

最近在公司项目里用 GitHub Copilot，说实话挺好用的，但有个问题让我很头疼——每次让它帮我生成 Git 提交信息都要消耗额度。
![28.png](/assets/blog/28.png)
你们知道那种感觉吗？就是改了一行代码，修个小 bug，结果为了写个提交信息还得"浪费"Copilot 的额度。特别是月底的时候，额度快用完了，每次用都心疼。有时候甚至为了省额度，又回到了手写提交信息的老路上。

作为一个懒癌晚期的程序员，我觉得这事儿不能忍。既然 Copilot 能做，我为什么不能自己做一个？而且还要做得更好用！

## 我的解决方案

花了几个周末的时间，我搞出了这个**LaFu AI Git Commit**插件。说是几个周末，其实主要是第一个周末写了个基础版本，后面几个周末都在优化和加功能。

这个插件最大的特点就是——**默认完全免费**。不需要任何 API key，装上就能用。当然，如果你想要更智能的效果，也可以接入各种 AI 服务。

### 主要特点

**本地生成，不花钱**
最开始我就想着，能不能不依赖任何外部 API，纯本地分析代码变更来生成提交信息？研究了一下 git diff 的输出格式，发现还真能做到。虽然没有 AI 那么智能，但对于日常的提交来说完全够用了。

**支持多种 AI 服务**
后来想着，既然都做了，不如把 AI 功能也加上。支持了 OpenAI、Claude、Gemini，还有国内的通义千问（这个对国内用户比较友好）。

**永远不会失败**
这是我最得意的设计。AI 服务挂了？没关系，自动切换到本地生成。网络不好？没关系，本地生成不需要网络。API 额度用完了？还是没关系，本地生成永远免费。

**操作简单**
就是源码管理面板上的一个小按钮，点一下就搞定。我自己用了几个月，基本上已经形成肌肉记忆了。

## 具体怎么实现的

### 本地生成的逻辑

本地生成其实就是分析 git diff 的输出。我写了个简单的算法：

- 统计新增和删除的行数
- 看看改了哪些文件
- 根据变更的规模和文件类型，判断是 feat、fix 还是 refactor
- 按照 Conventional Commits 的格式生成提交信息

比如你改了`src/extension.ts`，加了 100 多行代码，它就会生成：

```
feat: 为 src/extension.ts 添加新功能

- 新增 120 行
- 删除 15 行
```

虽然不如 AI 那么智能，但至少比`update`、`fix bug`这种提交信息强多了。

### AI 生成的效果

如果你配置了 AI 服务，效果就明显好很多了。比如同样的变更，AI 可能会生成：

```
feat: 实现AI驱动的提交信息生成功能

集成多个AI提供商支持，添加智能回退机制和可配置的生成风格
```

明显更有描述性，也更专业。

### 配置选项

我尽量让配置简单，但该有的选项还是要有：

- 可以选择中文或英文提交信息
- 支持不同的提交风格（我个人比较喜欢 Conventional Commits）
- AI 参数可以调节，比如创造性程度
- API 密钥建议用环境变量，比较安全

## 技术上的一些细节

### 多 AI 支持的实现

为了支持不同的 AI 服务，我做了个统一的接口。OpenAI 和通义千问用的是 OpenAI SDK（通义千问支持 OpenAI 格式），Claude 和 Gemini 就直接调 API。

```typescript
// 支持OpenAI格式的用统一SDK
if (config.provider === "openai" || config.provider === "tongyi") {
  return await callWithOpenAISDK(prompt, config);
}

// 其他的用专门的API调用
switch (config.provider) {
  case "claude":
    return await callClaude(prompt, config);
  case "gemini":
    return await callGemini(prompt, config);
}
```

### Prompt 的设计

这个花了不少时间调试。不同的 AI 对 prompt 的理解不一样，而且中英文的 prompt 也要分别优化。最终的效果还算满意。

### 错误处理

这个很重要。AI 服务不稳定是常事，所以我做了很多容错：

- AI 失败了自动用本地生成
- 网络超时有友好提示
- 可以重试，不用重新操作

## 怎么用

安装很简单，VS Code 扩展市场搜"LaFu AI Git Commit"就行。装完就能用，不需要任何配置。

![29.png](/assets/blog/git-ai.png)

日常使用就是：

1. 改完代码，`git add .`
2. 打开源码管理面板（Ctrl+Shift+G）
3. 点那个小星星按钮
4. 生成的提交信息检查一下，没问题就提交

我现在基本上每次提交都用这个，确实省了不少时间。

## 用户反馈

发布到现在，收到了一些不错的反馈。有人说本地生成功能很实用，不用担心网络问题。也有国内的朋友说通义千问的支持很贴心，生成的中文提交信息比较自然。

当然也有一些改进建议，我都记在 TODO 列表里了。

## 后续计划

有几个功能在考虑中：

- 自定义模板功能，让用户可以定义自己的提交格式
- 团队规范支持，可以配置团队级别的提交规范
- 基于历史提交的学习功能（这个技术上有点复杂，还在研究）
- 更多 AI 服务的支持

不过这些都要看时间，毕竟是业余时间在做。

## 最后

这个插件解决了我自己的痛点，希望也能帮到有同样困扰的朋友。虽然功能还不是特别完善，但基本的需求应该能满足。

如果你也经常为写 Git 提交信息发愁，或者不想浪费 Copilot 的额度，可以试试这个插件。有什么问题或建议，欢迎在 GitHub 上提 Issue。

代码写得不算特别优雅，但能用。开源出来也是希望大家一起改进。

---

**项目地址**：[GitHub - lafucode-ai-git-commit](https://github.com/pythonsir/lafucode-ai-git-commit)

**VS Code 扩展市场**：搜索"LaFu AI Git Commit"

**官网**：[https://lafucode.com](https://lafucode.com)

---

_如果这篇文章对你有帮助，欢迎点赞分享。如果你在使用过程中遇到任何问题，也欢迎在 GitHub 上提 Issue，我会及时回复和解决。_
