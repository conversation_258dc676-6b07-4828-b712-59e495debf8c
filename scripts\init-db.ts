#!/usr/bin/env tsx

// 加载环境变量
import { config } from 'dotenv'
import { resolve } from 'path'

config({ path: resolve(__dirname, '../.env') })

import { initDatabase } from '../src/lib/db/init'

async function main() {
  console.log('开始初始化数据库...')
  
  const success = await initDatabase()
  
  if (success) {
    console.log('✅ 数据库初始化成功！')
    process.exit(0)
  } else {
    console.log('❌ 数据库初始化失败！')
    process.exit(1)
  }
}

main().catch((error) => {
  console.error('初始化过程中发生错误:', error)
  process.exit(1)
})
