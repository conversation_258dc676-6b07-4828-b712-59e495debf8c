{"meta": {"title": "MySQL Password Hash Generator - Online MySQL Hash Tool", "description": "The world's simplest online MySQL password hash generator for web developers and programmers. Supports PASSWORD(), OLD_PASSWORD(), SHA1, SHA256, MD5 and other hash algorithms.", "keywords": "MySQL password hash,MySQL PASSWORD,hash generator,password encryption,database security,online tool"}, "title": "MySQL Password Hash Generator", "description": "Convert plain text passwords to MySQL hash values with support for multiple hash algorithms", "breadcrumb": {"home": "Home", "tools": "Tools", "current": "MySQL Hash Generator"}, "settings": {"title": "Hash Algorithm Selection", "password": "PASSWORD() - MySQL 4.1+", "oldPassword": "OLD_PASSWORD() - Before MySQL 4.1"}, "input": {"title": "Input Password", "password": "Plain Text Password", "placeholder": "Enter the password to generate hash..."}, "output": {"title": "Generated Hash", "hash": "Hash Value"}, "controls": {"generate": "Generate Hash", "copy": "Copy", "clear": "Clear"}, "examples": {"title": "Usage Examples", "createUser": "Create User", "setPassword": "Set Password"}, "messages": {"emptyPassword": "Please enter a password!", "copied": "Hash value copied to clipboard!"}, "tips": {"title": "Security Tips", "tip1": "PASSWORD() function is deprecated in MySQL 5.7.6, consider using more secure authentication plugins", "tip2": "OLD_PASSWORD() is only for compatibility with versions before MySQL 4.1, has lower security", "tip3": "Generated hash values can be directly used for MySQL user management and password settings", "tip4": "Please ensure using this tool in a secure environment, avoid processing sensitive passwords on public networks"}}