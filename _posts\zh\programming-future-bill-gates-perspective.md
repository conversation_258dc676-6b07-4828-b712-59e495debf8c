---
title: "比尔·盖茨说编程还能干100年，我信了"
excerpt: "最近看到比尔·盖茨说编程至少还能干100年，作为一个写了十几年代码的老程序员，我想聊聊为什么AI再厉害，也替代不了我们这群敲键盘的。"
coverImage: "/assets/blog/27.png"
featured: true
featuredOrder: 1
date: "2025-07-22"
lastModified: "2025-07-22"
author:
  name: 老夫撸代码
  picture: "/assets/blog/authors/tim.jpeg"
---

最近刷到比尔·盖茨的一个访谈，他说编程这个职业至少还能干 100 年。说实话，作为一个写了十几年代码的老程序员，听到这话还挺欣慰的。

现在 AI 工具确实厉害，GitHub Copilot 能帮我写代码，ChatGPT 能解释复杂的算法，Claude 能帮我重构项目。有时候我都怀疑，是不是哪天 AI 就把我们程序员给替代了？

但仔细想想，比尔·盖茨这话还真有道理。

## 为什么 AI 替代不了程序员？

### 写代码只是程序员工作的一小部分

很多人以为程序员就是写代码的，其实不是。我每天的工作里，真正敲代码的时间可能只占 30%。更多时候我在干什么？

想需求。产品经理说要做个功能，我得琢磨这个功能到底要解决什么问题，用户会怎么用，可能遇到什么坑。

设计架构。这个功能怎么和现有系统结合？数据库怎么设计？接口怎么定义？这些都需要经验和判断。

和人沟通。跟产品聊需求，跟设计师讨论交互，跟测试解释 bug，跟运维商量部署方案。

AI 能帮我写代码，但它能替我开会吗？能替我和产品经理撕逼吗？

举个例子，AI 能帮我写出这样的代码：

```typescript
function calculateTotal(items: Item[]): number {
  return items.reduce((sum, item) => sum + item.price, 0);
}
```

但 AI 不会问我：

- 这个计算逻辑对不对？万一 price 是负数怎么办？
- 用户购物车里有几千个商品，这样计算会不会太慢？
- 这个函数放在前端还是后端？安全性怎么保证？
- 如果以后要支持优惠券、积分抵扣，这个设计还能用吗？

### 程序员最值钱的是判断力

写了这么多年代码，我发现最难的不是技术实现，而是做选择。

用 React 还是 Vue？MySQL 还是 PostgreSQL？微服务还是单体？每个选择背后都有无数的考量。性能、成本、团队技能、项目周期、未来扩展...这些因素怎么权衡？

AI 能告诉我每种技术的优缺点，但它做不了决定。因为它不知道我们公司的具体情况，不知道团队的技术水平，不知道老板的预算有多少。

## AI 是我的好搭档，不是竞争对手

### 现在我是怎么和 AI 协作的

说实话，现在我已经离不开 AI 了。但我把它当工具，不是威胁。

**AI 帮我干的事：**

- 写那些重复的 CRUD 代码
- 生成单元测试
- 解释复杂的正则表达式
- 帮我 review 代码找 bug

**我自己负责的事：**

- 理解产品需求，知道用户要什么
- 设计系统架构，考虑扩展性和维护性
- 做技术选型，选择最适合的方案
- 处理各种奇葩的边界情况

### 我的工作流程变了

以前写代码是这样的：需求 → 设计 → 编码 → 测试 → 部署

现在是这样的：

1. 我分析需求，理解要解决什么问题
2. 我设计整体架构和数据流
3. AI 帮我生成基础代码
4. 我 review 代码，调整逻辑
5. AI 帮我写测试用例
6. 我负责集成和优化
7. 我决定怎么部署上线

看出来了吗？AI 负责体力活，我负责脑力活。

## 程序员这个职业在变化

### 从码农变成架构师

以前大家叫我们码农，现在我觉得我们更像建筑师。

建筑师不会亲自搬砖，但他们设计房子的结构，选择材料，协调各个工种。程序员也一样，我们不用写每一行代码，但我们要设计系统，选择技术，协调团队。

### 从实现功能到创造价值

以前产品经理说要什么功能，我们就实现什么功能。现在不一样了，我们要思考这个功能有没有价值，用户会不会用，有没有更好的解决方案。

我们要懂产品，懂用户，懂商业。技术只是手段，创造价值才是目的。

### 专业化越来越重要

现在的程序员不能再是万金油了，得有自己的专长：

- **AI 工程师**：专门搞机器学习和深度学习
- **安全专家**：防黑客、防数据泄露
- **性能优化师**：让系统跑得更快更稳
- **DevOps 工程师**：搞自动化部署和运维

我自己选择了全栈开发这个方向，前端后端都能搞，适合小团队和创业公司。

## 程序员需要什么新技能？

### 要有全局思维

以前我只关心自己负责的模块，现在我要考虑整个系统。

比如做一个用户注册功能，我不能只写个接口就完事了。我要想：

- 注册流程对用户友好吗？
- 数据库设计能支持未来的扩展吗？
- 如果用户量暴增，系统能扛住吗？
- 怎么防止恶意注册和数据泄露？
- 注册失败了用户能得到清楚的提示吗？

```typescript
// 不只是写个简单的注册函数
function registerUser(email: string, password: string) {
  // 简单实现
}

// 而是要考虑完整的用户生命周期
class UserService {
  async register(userData: RegisterData) {
    // 数据验证、重复检查、密码加密
    // 发送验证邮件、记录日志、监控指标
    // 错误处理、回滚机制、通知相关系统
  }
}
```

### 要懂业务，不只是技术

现在的程序员不能只懂技术，还要懂：

- **行业知识**：你做电商就要懂电商，做金融就要懂金融
- **用户体验**：站在用户角度思考产品
- **数据分析**：通过数据了解用户行为
- **商业模式**：知道公司怎么赚钱

### 要保持学习

技术更新太快了，不学习就被淘汰。但现在学习方式也变了：

- **快速上手**：新技术出来，能快速试用和评估
- **深度思考**：不盲目追新，要判断技术的价值
- **举一反三**：把学到的知识应用到不同场景

## 给程序员同行的几个建议

### 别怕 AI，学会用 AI

我身边还有同事觉得用 AI 写代码是"作弊"，这想法太落后了。AI 就是工具，就像我们用 IDE、用框架一样。

我现在的工作流程：

1. 我用自然语言描述需求
2. AI 生成基础代码
3. 我 review 和优化
4. AI 帮我写测试和文档

效率提升了至少 50%，而且代码质量更稳定。

### 软技能比硬技能更重要

技术好的程序员很多，但能和人打交道的程序员不多。现在这个能力越来越值钱：

- **会沟通**：能把技术问题用人话解释清楚
- **会管理**：能协调资源，推进项目
- **会合作**：能在跨部门团队里发挥作用

### 打造个人影响力

现在是个人品牌的时代，程序员也要有自己的影响力：

- **写技术博客**：分享你的经验和思考
- **参与开源**：为社区贡献代码
- **技术分享**：在公司内部或外部做分享

## 写在最后

比尔·盖茨说编程还能干 100 年，我是信的。

因为编程从来不只是写代码，而是解决问题。AI 能帮我们写代码，但它解决不了所有问题。

我们程序员最宝贵的不是敲键盘的速度，而是：

- **创造力**：能想出别人想不到的解决方案
- **判断力**：在复杂情况下做出正确选择
- **同理心**：理解用户真正需要什么
- **责任心**：对自己写的代码负责

未来的程序员会是什么样？我觉得更像是**人机协作的指挥官**。我们指挥 AI 干活，自己专注于思考和创造。

这不是程序员的末日，而是新时代的开始。关键是要拥抱变化，学会和 AI 合作，同时保持自己的独特价值。

**程序员的未来，属于那些能和 AI 协作，又保持人类独特价值的人。**

我们还能干 100 年，你信吗？
