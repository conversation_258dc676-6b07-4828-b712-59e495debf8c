---
title: "<PERSON> Says Programming Will Last 100 More Years - I Believe Him"
excerpt: "Recently saw <PERSON> saying programming will last at least 100 more years. As a programmer who's been coding for over a decade, I want to share why AI, no matter how powerful, can't replace us keyboard warriors."
coverImage: "/assets/blog/27.png"
featured: false
featuredOrder: 1
date: "2025-07-22"
lastModified: "2025-07-22"
author:
  name: <PERSON><PERSON> <PERSON>
  picture: "/assets/blog/authors/tim.jpeg"
---

Recently came across an interview with <PERSON> where he said programming as a profession will last at least 100 more years. Honestly, as a programmer who's been coding for over a decade, hearing this was pretty reassuring.

AI tools are indeed powerful these days. GitHub Copilot helps me write code, ChatGPT explains complex algorithms, <PERSON> helps me refactor projects. Sometimes I wonder if AI will eventually replace us programmers.

But thinking about it, <PERSON> really has a point.

## Why AI Can't Replace Programmers

### Coding Is Just a Small Part of What Programmers Do

Many people think programmers just write code, but that's not true. In my daily work, actual coding probably takes up only 30% of my time. What am I doing the rest of the time?

Thinking about requirements. When a product manager says they want a feature, I need to figure out what problem this feature actually solves, how users will use it, what pitfalls we might encounter.

Designing architecture. How does this feature integrate with existing systems? How should the database be designed? How should APIs be defined? These all require experience and judgment.

Communicating with people. Talking requirements with product managers, discussing interactions with designers, explaining bugs to testers, planning deployment with DevOps.

AI can help me write code, but can it replace me in meetings? Can it argue with product managers for me?

For example, AI can help me write code like this:

```typescript
function calculateTotal(items: Item[]): number {
  return items.reduce((sum, item) => sum + item.price, 0);
}
```

But AI won't ask me:

- Is this calculation logic correct? What if price is negative?
- What if users have thousands of items in their cart? Will this be too slow?
- Should this function be on frontend or backend? How do we ensure security?
- If we need to support coupons and points redemption later, will this design still work?

### A Programmer's Most Valuable Asset Is Judgment

After coding for so many years, I've found that the hardest part isn't technical implementation, but making choices.

React or Vue? MySQL or PostgreSQL? Microservices or monolith? Every choice involves countless considerations. Performance, cost, team skills, project timeline, future scalability... How do you weigh these factors?

AI can tell me the pros and cons of each technology, but it can't make the decision. Because it doesn't know our company's specific situation, doesn't know the team's technical level, doesn't know how much budget the boss has.

## AI Is My Good Partner, Not a Competitor

### How I Collaborate with AI Now

Honestly, I can't live without AI anymore. But I treat it as a tool, not a threat.

**What AI helps me with:**

- Writing repetitive CRUD code
- Generating unit tests
- Explaining complex regular expressions
- Helping me review code and find bugs

**What I handle myself:**

- Understanding product requirements, knowing what users want
- Designing system architecture, considering scalability and maintainability
- Making technology choices, selecting the most suitable solutions
- Handling various weird edge cases

### My Workflow Has Changed

Before, coding was like: Requirements → Design → Coding → Testing → Deployment

Now it's like this:

1. I analyze requirements, understand what problem to solve
2. I design overall architecture and data flow
3. AI helps me generate basic code
4. I review code and adjust logic
5. AI helps me write test cases
6. I handle integration and optimization
7. I decide how to deploy

See the pattern? AI handles the manual labor, I handle the brain work.

## The Programming Profession Is Changing

### From Code Monkey to Architect

People used to call us code monkeys, but now I think we're more like architects.

Architects don't lay bricks themselves, but they design the structure of buildings, choose materials, coordinate different trades. Programmers are the same - we don't need to write every line of code, but we design systems, choose technologies, coordinate teams.

### From Implementing Features to Creating Value

Before, when product managers said they wanted a feature, we just implemented it. Now it's different - we need to think about whether this feature has value, whether users will use it, whether there are better solutions.

We need to understand products, users, and business. Technology is just a means; creating value is the goal.

### Specialization Is Becoming More Important

Since AI can handle basic coding, we need to become experts in specific areas. You can't be a generalist anymore.

For example:

- AI/ML engineers who understand algorithms and model optimization
- Security engineers who know how to prevent hackers
- Performance engineers who can make systems run faster
- DevOps engineers who automate everything
- Domain experts who understand specific industries

I chose full-stack development because I like touching everything from frontend to backend to deployment. But even within full-stack, I need to go deeper.

## What Skills Do I Need to Develop?

### Think About the Whole System, Not Just Individual Functions

Before, I only cared about whether my function worked. Now I need to think about the entire system.

```typescript
// Old me: Just make it work
function processUserData(userData: UserData): ProcessedData {
  // Implementation logic
}

// Current me: Consider the whole picture
class UserDataPipeline {
  constructor(private validator: DataValidator, private processor: DataProcessor, private storage: DataStorage, private notifier: NotificationService) {}

  async processUser(userData: UserData): Promise<void> {
    // What if validation fails? What if the database is down?
    // What if processing takes too long? How do we monitor this?
    try {
      const validData = await this.validator.validate(userData);
      const processedData = await this.processor.process(validData);
      await this.storage.save(processedData);
      await this.notifier.notify(processedData);
    } catch (error) {
      // Error handling and recovery strategies
    }
  }
}
```

See the difference? I'm not just writing code, I'm designing systems.

### Learn Beyond Just Coding

I used to think knowing programming languages was enough. Now I realize I need to understand:

- **The Business**: What problem are we actually solving? Why do users need this?
- **User Experience**: Is this feature easy to use? Will users understand it?
- **Data**: What do the numbers tell us? Are users actually using what we built?
- **Infrastructure**: How do we make sure our app doesn't crash when a million people use it?

### Never Stop Learning (But Learn Smart)

Technology changes so fast. Every month there's a new JavaScript framework. But I've learned not to chase every shiny new thing:

- **Learn the fundamentals well**: Once you understand the basics, new frameworks are just variations
- **Ask "why" before "how"**: Why was this technology created? What problem does it solve?
- **Connect the dots**: How does this relate to what I already know?

## My Advice for Fellow Programmers

### Embrace AI, Don't Fight It

I see some programmers afraid of AI, thinking it will replace them. That's like being afraid of calculators because they can do math faster than us.

- **Learn the tools**: GitHub Copilot, ChatGPT, Claude - they're part of our toolkit now
- **Know their limits**: AI can write code, but it can't understand your users
- **Use them smart**: Let AI handle the boring stuff, you focus on the interesting problems

### Soft Skills Matter More Than Ever

When AI can write code, what makes you valuable? Your ability to work with people.

- **Communication**: Can you explain technical stuff to your boss who doesn't code?
- **Collaboration**: Can you work with designers, product managers, marketers?
- **Leadership**: Can you guide a team through tough technical decisions?
- **Empathy**: Do you understand what users actually need?

### Build Your Reputation

In the AI era, your personal brand matters more than your resume.

- **Write about what you learn**: Start a blog, share your experiences. Even if only 10 people read it, those might be the right 10 people
- **Contribute to open source**: It's not just about the code, it's about showing you can work with others
- **Join communities**: Go to meetups, conferences, online forums. Network with people, learn from them
- **Stay curious**: The moment you stop learning is the moment you become irrelevant

## Final Thoughts

Bill Gates says programming will last another 100 years. I believe him.

Because programming isn't really about writing code. It's about solving problems. And humans are really good at that.

AI can write code faster than me. But it can't:

- Understand what users actually want (not what they say they want)
- Make tough decisions when there's no clear right answer
- Feel empathy for the person struggling to use our software
- Take responsibility when things go wrong

We're not becoming obsolete. We're becoming **human-AI collaboration leaders**. We use AI as our superpower tool, but we keep the human wisdom.

The programming profession isn't dying - it's growing up. Those of us who embrace this change, who learn to work with AI while staying human, we're going to do just fine.

After all, the best programs aren't just technically perfect. They solve real problems for real people. And that's something only humans can truly understand.
