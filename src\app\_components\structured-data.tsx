import { SITE_NAME, SITE_URL, SITE_DESCRIPTION, SITE_AUTHOR } from "@/lib/constants";

interface StructuredDataProps {
  type: 'website' | 'blog' | 'article';
  title?: string;
  description?: string;
  url?: string;
  datePublished?: string;
  dateModified?: string;
  author?: string;
  image?: string;
}

export function StructuredData({
  type,
  title,
  description,
  url,
  datePublished,
  dateModified,
  author,
  image
}: StructuredDataProps) {
  const baseData = {
    "@context": "https://schema.org",
    "@type": type === 'website' ? "WebSite" : type === 'blog' ? "Blog" : "BlogPosting",
    "name": title || SITE_NAME,
    "description": description || SITE_DESCRIPTION,
    "url": url || SITE_URL,
    "publisher": {
      "@type": "Organization",
      "name": SITE_NAME,
      "url": SITE_URL,
    }
  };

  if (type === 'website') {
    return (
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            ...baseData,
            "potentialAction": {
              "@type": "SearchAction",
              "target": `${SITE_URL}/posts?search={search_term_string}`,
              "query-input": "required name=search_term_string"
            }
          })
        }}
      />
    );
  }

  if (type === 'article') {
    return (
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            ...baseData,
            "headline": title,
            "author": {
              "@type": "Person",
              "name": author || SITE_AUTHOR,
              "url": SITE_URL
            },
            "datePublished": datePublished,
            "dateModified": dateModified || datePublished,
            "image": image,
            "mainEntityOfPage": {
              "@type": "WebPage",
              "@id": url
            }
          })
        }}
      />
    );
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(baseData)
      }}
    />
  );
}
