import { getTranslations } from 'next-intl/server';
import { Metadata } from 'next';
import { SITE_URL } from '@/lib/constants';
import Image from 'next/image';
import AboutPageClient from './AboutPageClient';

type Props = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.about' });
  const tSite = await getTranslations({ locale, namespace: 'site' });

  return {
    title: `${t('title')} | ${tSite('name')}`,
    description: t('subtitle'),
    authors: [{ name: tSite('author') }],
    creator: tSite('author'),
    publisher: tSite('name'),
    metadataBase: new URL(SITE_URL),
    alternates: {
      canonical: locale === 'en' ? '/en/about' : '/about',
      languages: {
        'zh': '/about',
        'en': '/en/about',
      },
    },
    openGraph: {
      title: `${t('title')} | ${tSite('name')}`,
      description: t('subtitle'),
      url: locale === 'en' ? '/en/about' : '/about',
      siteName: tSite('name'),
      locale: locale === 'zh' ? 'zh_CN' : 'en_US',
      type: 'website',
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

export default async function AboutPage({ params }: Props) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.about' });
  
  // Pre-fetch all translation data
  const translations = {
    title: t('title'),
    subtitle: t('subtitle'),
    authorName: t('authorName'),
    skills: {
      frontend: {
        title: t('skills.frontend.title'),
        items: [
          t('skills.frontend.items.0'),
          t('skills.frontend.items.1'),
          t('skills.frontend.items.2')
        ]
      },
      backend: {
        title: t('skills.backend.title'),
        items: [
          t('skills.backend.items.0'),
          t('skills.backend.items.1'),
          t('skills.backend.items.2')
        ]
      },
      mobile: {
        title: t('skills.mobile.title'),
        items: [
          t('skills.mobile.items.0'),
          t('skills.mobile.items.1'),
          t('skills.mobile.items.2'),
          t('skills.mobile.items.3'),
          t('skills.mobile.items.4')
        ]
      },
      project: {
        title: t('skills.project.title'),
        items: [
          t('skills.project.items.0'),
          t('skills.project.items.1'),
          t('skills.project.items.2')
        ]
      }
    },
    philosophy: {
      title: t('philosophy.title'),
      practical: {
        title: t('philosophy.practical.title'),
        description: t('philosophy.practical.description')
      },
      accessible: {
        title: t('philosophy.accessible.title'),
        description: t('philosophy.accessible.description')
      },
      learning: {
        title: t('philosophy.learning.title'),
        description: t('philosophy.learning.description')
      },
      communication: {
        title: t('philosophy.communication.title'),
        description: t('philosophy.communication.description')
      }
    },
    techStack: {
      title: t('techStack.title'),
      frontend: {
        title: t('techStack.frontend.title')
      },
      backend: {
        title: t('techStack.backend.title')
      },
      tools: {
        title: t('techStack.tools.title')
      }
    },
    contact: {
      title: t('contact.title'),
      description: t('contact.description'),
      sendEmail: t('contact.sendEmail'),
      github: t('contact.github')
    }
  };
  
  return <AboutPageClient locale={locale} translations={translations} />;
}