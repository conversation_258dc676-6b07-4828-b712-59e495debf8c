"use client";

import { useState, useMemo } from "react";
import { type Post } from "@/interfaces/post";
import { PostListItem } from "./post-list-item";
import { PaginationClient } from "./pagination-client";
import { trackEvent } from "@/lib/gtag";

type TranslationsType = {
  searchPlaceholder: string;
  sortByDate: string;
  sortByTitle: string;
  filterAll: string;
  filterFeatured: string;
  resultsFoundPrefix: string;
  resultsFoundSuffix: string;
  resultsWithKeywordPrefix: string;
  resultsWithKeywordSuffix: string;
  noResultsTitle: string;
  noResultsDescription: string;
  resetFilters: string;
  browseAllPosts: string;
};

type PaginationTranslationsType = {
  previous: string;
  next: string;
  showingItemsPrefix: string;
  showingItemsMiddle1: string;
  showingItemsMiddle2: string;
  showingItemsSuffix: string;
  pageInfoPrefix: string;
  pageInfoMiddle: string;
  pageInfoSuffix: string;
};

type Props = {
  posts: Post[];
  locale: string;
  translations: TranslationsType;
  paginationTranslations: PaginationTranslationsType;
};

export function SearchAndFilterClient({ posts, locale, translations: t, paginationTranslations }: Props) {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<"date" | "title">("date");
  const [filterBy, setFilterBy] = useState<"all" | "featured">("all");
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 12; // 每页显示的文章数量

  // 过滤和排序文章
  const filteredAndSortedPosts = useMemo(() => {
    let filtered = posts;

    // 搜索过滤
    if (searchTerm) {
      filtered = filtered.filter(post =>
        post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.excerpt.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // 类型过滤
    if (filterBy === "featured") {
      filtered = filtered.filter(post => post.featured === true);
    }

    // 排序
    filtered.sort((a, b) => {
      if (sortBy === "date") {
        return new Date(b.date).getTime() - new Date(a.date).getTime();
      } else {
        return a.title.localeCompare(b.title);
      }
    });

    return filtered;
  }, [posts, searchTerm, sortBy, filterBy]);

  // 分页计算
  const totalPages = Math.ceil(filteredAndSortedPosts.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentPosts = filteredAndSortedPosts.slice(startIndex, endIndex);

  // 当搜索或筛选条件改变时，重置到第一页
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);

    // 跟踪搜索事件（只在有搜索词时跟踪）
    if (value.trim()) {
      trackEvent.search(value.trim(), filteredAndSortedPosts.length);
    }
  };

  const handleSortChange = (value: "date" | "title") => {
    setSortBy(value);
    setCurrentPage(1);
  };

  const handleFilterChange = (value: "all" | "featured") => {
    setFilterBy(value);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // 滚动到页面顶部
    if (typeof window !== 'undefined') {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  // 格式化翻译字符串
  const formatResultsFound = (count: number) => {
    return t.resultsFoundPrefix + count.toString() + t.resultsFoundSuffix;
  };

  const formatResultsWithKeyword = (keyword: string) => {
    return t.resultsWithKeywordPrefix + keyword + t.resultsWithKeywordSuffix;
  };

  return (
    <div>
      {/* 搜索和筛选控件 */}
      <div className="backdrop-blur-sm rounded-2xl mb-8">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* 搜索框 */}
          <div className="flex-1">
            <div className="relative group">
              <svg className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400 group-focus-within:text-blue-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              <input
                type="text"
                placeholder={t.searchPlaceholder}
                value={searchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="w-full pl-12 pr-4 py-3.5 bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 placeholder:text-slate-400 text-slate-700 dark:text-slate-200 shadow-sm focus:shadow-md"
              />
              {searchTerm && (
                <button
                  onClick={() => handleSearchChange("")}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 text-slate-400 hover:text-slate-600 transition-colors duration-200"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              )}
            </div>
          </div>

          {/* 排序选择 */}
          <div className="lg:w-52">
            <div className="relative">
              <select
                value={sortBy}
                onChange={(e) => handleSortChange(e.target.value as "date" | "title")}
                className="w-full px-4 py-3.5 bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 appearance-none cursor-pointer text-slate-700 dark:text-slate-200 shadow-sm focus:shadow-md"
              >
                <option value="date">{t.sortByDate}</option>
                <option value="title">{t.sortByTitle}</option>
              </select>
              <svg className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400 pointer-events-none" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>

          {/* 类型筛选 */}
          <div className="lg:w-52">
            <div className="relative">
              <select
                value={filterBy}
                onChange={(e) => handleFilterChange(e.target.value as "all" | "featured")}
                className="w-full px-4 py-3.5 bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 appearance-none cursor-pointer text-slate-700 dark:text-slate-200 shadow-sm focus:shadow-md"
              >
                <option value="all">{t.filterAll}</option>
                <option value="featured">{t.filterFeatured}</option>
              </select>
              <svg className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400 pointer-events-none" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* 结果统计 */}
      <div className="mb-6">
        <p className="text-sm text-slate-600 dark:text-slate-300">
          {formatResultsFound(filteredAndSortedPosts.length)}
          {searchTerm && (
            <span>
              {formatResultsWithKeyword(searchTerm)}
            </span>
          )}
        </p>
      </div>

      {/* 文章列表 */}
      {currentPosts.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {currentPosts.map((post) => (
            <div key={post.slug} className="group bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 border border-slate-200/50 dark:border-slate-700/50 hover:border-blue-300/50 dark:hover:border-blue-600/50">
              <PostListItem
                title={post.title}
                coverImage={post.coverImage}
                date={post.date}
                excerpt={post.excerpt}
                author={post.author}
                slug={post.slug}
                featured={post.featured}
                content={post.content}
              />
            </div>
          ))}
        </div>
      ) : (
        <div className="w-full flex flex-col items-center justify-center text-center py-20 px-4 min-h-[400px]">
          <div className="w-32 h-32 mx-auto mb-6 bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-700 dark:to-slate-600 rounded-full flex items-center justify-center shadow-lg">
            <svg className="w-16 h-16 text-slate-400 dark:text-slate-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-3">
            {t.noResultsTitle}
          </h3>
          <p className="text-base text-slate-600 dark:text-slate-300 mb-6 max-w-md">
            {t.noResultsDescription}
          </p>
          <div className="flex flex-col sm:flex-row gap-3">
            <button
              onClick={() => {
                handleSearchChange("");
                handleFilterChange("all");
              }}
              className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              {t.resetFilters}
            </button>
            <button
              onClick={() => {
                if (typeof window !== 'undefined') {
                  window.location.href = `/${locale === 'en' ? 'en/' : ''}posts`;
                }
              }}
              className="px-6 py-3 bg-slate-100 hover:bg-slate-200 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-300 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
              {t.browseAllPosts}
            </button>
          </div>
        </div>
      )}

      {/* 分页组件 */}
      {filteredAndSortedPosts.length > 0 && (
        <PaginationClient
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          translations={paginationTranslations}
          totalItems={filteredAndSortedPosts.length}
          itemsPerPage={itemsPerPage}
        />
      )}
    </div>
  );
}
