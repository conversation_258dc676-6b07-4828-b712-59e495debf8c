// Giscus评论系统配置
// 请根据你的GitHub仓库信息修改以下配置

// 是否启用评论系统 (设置为false可以临时禁用评论)
export const ENABLE_COMMENTS = true;

export const giscusConfig = {
  // GitHub仓库信息 (格式: "用户名/仓库名")
  // 您的评论仓库
  repo: "pythonsir/lafucode-comments" as const,

  // 仓库ID (已配置)
  repoId: "R_kgDOPEuajg",

  // 讨论分类
  category: "General",

  // 分类ID (已配置)
  categoryId: "DIC_kwDOPEuajs4CsShI",

  // 映射方式 - 使用pathname更可靠，避免中文标题编码问题
  mapping: "pathname" as const,

  // 严格匹配模式
  strict: "0" as const,

  // 是否启用反应
  reactionsEnabled: "1" as const,

  // 是否发送元数据
  emitMetadata: "0" as const,

  // 输入框位置
  inputPosition: "top" as const,

  // 语言
  lang: "zh-CN" as const,

  // 加载方式
  loading: "lazy" as const,
};

// ✅ 配置已完成！评论系统已就绪
//
// 📋 配置状态：
// ✅ 仓库: pythonsir/lafucode-comments (已验证)
// ✅ 仓库ID: R_kgDOPEuajg (已配置)
// ✅ 分类: General (已配置)
// ✅ 分类ID: DIC_kwDOPEuajs4CsShI (已配置)
// ✅ Discussions功能: 已启用
// ✅ 仓库权限: 公开
//
// 🎯 使用说明：
// 1. 用户访问文章页面时会看到评论区域
// 2. 首次评论时需要GitHub登录授权
// 3. Giscus会自动在您的仓库中创建对应的讨论
// 4. 所有评论都会同步到GitHub Discussions
//
// 🔧 管理评论：
// - 访问 https://github.com/pythonsir/lafucode-comments/discussions
// - 查看、管理、回复所有评论
// - 设置评论通知和权限
//
// 📊 验证配置：
// - 运行 `node scripts/verify-giscus.js` 验证配置
// - 404错误是正常的，表示还没有对应的讨论