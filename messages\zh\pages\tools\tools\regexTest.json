{"title": "🔍 正则表达式测试工具", "description": "在线正则表达式测试和验证工具，支持实时匹配和语法高亮", "breadcrumb": {"home": "首页", "tools": "工具箱", "current": "正则表达式测试"}, "sections": {"pattern": "正则表达式模式", "flags": "标志", "commonPatterns": "常用模式", "testString": "测试字符串", "highlighted": "高亮文本", "matchDetails": "匹配详情"}, "placeholders": {"pattern": "请输入正则表达式...", "flags": "gimsuvy", "testString": "请输入要测试的文本...", "highlightedEmpty": "输入模式和测试字符串以查看高亮匹配", "noMatches": "没有找到匹配项", "startTesting": "输入模式和测试字符串开始测试"}, "flags": {"label": "标志:", "g": "全局 (g)", "i": "忽略大小写 (i)", "m": "多行 (m)", "s": "单行 (s)", "u": "Unicode (u)", "y": "粘性 (y)"}, "commonPatterns": {"email": "邮箱", "phone": "手机", "idCard": "身份证", "ip": "IP地址", "url": "网址", "chinese": "中文", "number": "数字", "letter": "字母"}, "matchDetails": {"matchCount": "个匹配", "match": "匹配", "position": "位置", "captureGroups": "捕获组", "empty": "(空)"}, "errors": {"syntaxError": "无效的正则表达式语法"}, "actions": {"clear": "清空全部"}, "tips": {"title": "使用说明", "metacharacters": {"title": "元字符", "items": {"0": "<code>.</code> - 匹配除换行符外的任意字符", "1": "<code>^</code> - 匹配字符串开始", "2": "<code>$</code> - 匹配字符串结束", "3": "<code>*</code> - 匹配前面元素0次或多次", "4": "<code>+</code> - 匹配前面元素1次或多次", "5": "<code>?</code> - 匹配前面元素0次或1次"}}, "characterClasses": {"title": "字符类", "items": {"0": "<code>\\d</code> - 匹配任意数字 (0-9)", "1": "<code>\\w</code> - 匹配任意单词字符 (a-z, A-Z, 0-9, _)", "2": "<code>\\s</code> - 匹配任意空白字符", "3": "<code>[abc]</code> - 匹配集合中的任意字符", "4": "<code>[^abc]</code> - 匹配不在集合中的任意字符"}}}, "meta": {"title": "正则表达式测试工具 - 在线正则验证器", "description": "免费的在线正则表达式测试工具，支持实时匹配、语法高亮和捕获组显示。"}}