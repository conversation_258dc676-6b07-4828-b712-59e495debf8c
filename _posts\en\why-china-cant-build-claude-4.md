---
title: "Why Can't China Build Claude 4? Where Do We Really Fall Short?"
excerpt: "Claude 4's impressive capabilities once again highlight the AI technology gap between China and the US. As a tech professional, I want to discuss why China consistently fails to produce groundbreaking AI models like this, and where we really fall short."
coverImage: "/assets/blog/42.jpg"
featured: false
featuredOrder: 1
date: "2025-08-04"
lastModified: "2025-08-04"
author:
  name: <PERSON><PERSON> <PERSON>
  picture: "/assets/blog/authors/tim.jpeg"
---

I've been using Claude 4 again recently, and every time I finish using it, I have this complex feeling.

Honestly, every time I experience these top-tier AI models, I get this sense of "being left behind by a huge margin." It's not sour grapes, but I genuinely feel the gap is getting wider.

A few days ago, over dinner with a friend, we got into this topic. He asked me: "With so many smart people and so much money in China, why can't we create something like GPT or Claude?"

I thought about it, and this question is definitely worth discussing. Looking at the 0.01 yuan orange juice I got from a deal, I fell into deep thought.

To put it bluntly, our capital prefers making quick money, making money from ordinary people. They focus on people's grocery baskets and wallets, creating food delivery platforms and group-buying apps where they can immediately see cash flow.

Sometimes I really feel like we're the landlord's spoiled son—letting others develop the technology first, then we throw money at it, make acquisitions, package it for IPO, and sell to retail investors. Make a quick buck and run, then take the money overseas to live it up.

This approach works great for applications and business model innovation, but for fundamental technology? Forget about it.

## At First Glance, We Don't Seem to Lack Anything

### Talent? We Have Plenty

When it comes to talent, China really doesn't lack it. When I was at Tencent, the team next door had several big shots who came back from Google and Facebook. Those who graduated from Tsinghua's Yao Class are all genius-level.

I attended an AI conference recently, and the professors speaking on stage had tens of thousands of paper citations. Their technical level is definitely not inferior to those in Silicon Valley.

A friend of mine, a CMU machine learning PhD graduate, now works on large models at ByteDance. When we chat, he says the technical level of OpenAI people is just so-so, not as godlike as imagined.

### Money? Even Less of a Problem

When it comes to burning money, we're professionals.

Alibaba Cloud spent hundreds of billions just on GPUs last year, and Tencent Cloud wasn't far behind. ByteDance directly leased several data centers to train models.

I heard Baidu invested over a trillion yuan in Wenxin Yiyan. This figure is more than OpenAI's total investment since its founding.

### Computing Power? Good Enough

Although we're restricted by the US on high-end chips, we're not completely helpless.

Huawei's Ascend 910, while not comparable to A100, can still train smaller models. And we have quantity—pile them up and they work.

I've been to Alibaba Cloud's data center, and the scale is truly shocking. Rows and rows of servers, densely packed, just looking at it makes you feel there's abundant computing power.

## So Where's the Real Problem?

### We're Always Following, Rarely Leading

I've thought about this problem for a long time.

Look, Transformer was created by Google, GPT series by OpenAI, Constitutional AI (Claude's core technology) was invented by Anthropic. These game-changing technologies that laid the foundation for modern large models almost all come from the US.

What about us? Most of the time we're optimizing based on others' foundations. For example, after ChatGPT became popular, a bunch of Chinese companies started making "Chinese versions of ChatGPT." But honestly, they're just taking the Transformer architecture, swapping in Chinese data, and tuning parameters.

I worked at a big tech company for a while and witnessed such projects firsthand. The boss slammed the table saying we'd make "China's GPT," but after six months of work, what the team produced was essentially still spinning within others' frameworks.

Truly original breakthroughs from 0 to 1 are indeed rare.

### Short-term Thinking, Lack of Long-term Vision

This problem might be even more fatal.

Look at OpenAI—Sam Altman and his team have been betting on AGI since 2015. They nearly went bankrupt several times, investors weren't optimistic, but they persisted.

Anthropic is even more extreme. Dario Amodei left OpenAI to specifically research AI safety. This seemed to have zero commercial value at the time, pure idealism. But they persisted for several years and eventually created Claude.

What about us? I've seen too many stories like this:

Last year, my friend's company started a large model project. The boss was confident, saying they'd make "China's ChatGPT." After three months, they found training costs too high and switched to vertical domain small models. After another three months, finding poor results, it became a customer service chatbot.

This project still exists, but it's completely different from the original goal.

Investors are the same, constantly asking: "When can this be profitable? What's the business model?" Few are willing to invest in seemingly "abstract" fundamental research.

### Data Issues Are Really Headache-Inducing

I have deep experience with this problem.

When OpenAI trained GPT, they directly crawled the entire internet. Reddit, Twitter, Wikipedia, various forums, blogs—basically all public text was used for training. Although there were later copyright disputes, they indeed obtained massive high-quality data.

Our situation is more troublesome.

I have a friend at a big tech company working on large models who complained to me about this. Want to use a certain dataset? Legal says there are compliance risks. Want to crawl data from a website? Compliance says no, might involve privacy issues.

In the end, they could only use some public, pre-cleaned datasets. But the quality and quantity of these datasets, compared to directly crawling the entire internet, the gap is huge.

Moreover, high-quality content on the Chinese internet is already limited, with much good content locked in relatively closed platforms like WeChat, Zhihu, and TikTok. Getting this data is even more difficult.

### Talent Stays in Big Tech, Rarely Ventures Out

Another problem is talent mobility.

In Silicon Valley, many core team members at OpenAI came from Google Brain and DeepMind. Anthropic's founding team directly came from OpenAI. People jump between different companies, and technology and experience flow with them.

What about us? Top talent basically stays in big tech companies, and few come out to start businesses.

I know several technically excellent friends, all at Tencent, Alibaba, and other big companies. When I ask why they don't come out to do something, they say: "Big tech is pretty good—high salary, good benefits, why come out to suffer?"

Moreover, China's tech circle is relatively closed. Much core technology stays within companies, unlike Silicon Valley with many open-source projects. Everyone works in silos, lacking communication.

### Investors Only Look at Short-term Returns

The last problem is investment philosophy.

I've attended many AI project pitches, and the most common questions from investors are: "When can this be profitable? What's the business model? How willing are users to pay?"

Few ask: "What breakthrough does this technology have? Can it change the industry?"

Silicon Valley is different—they're willing to invest in seemingly "crazy" projects. When OpenAI was early stage, many thought AGI was fantasy, but investors were still willing to take the bet.

This difference makes it hard for us to support fundamental research that requires long-term investment with no short-term returns.

## But We Also Have Our Strengths

Having said so many problems, we shouldn't be overly self-critical. China does well in certain areas.

### Application Implementation Capability Is Strong

Although we can't match their fundamental models, our ability to implement AI technology is genuinely strong.

TikTok's recommendation algorithm, Meituan's delivery route optimization, Didi's intelligent scheduling—these are all world-class AI applications. User experience is better than many Silicon Valley companies.

We have deep understanding of user needs, knowing what Chinese users like, and can quickly package AI technology into products users love.

### Engineering Capability Is Unquestionable

Chinese programmers' engineering capability is globally recognized. Give us a technical solution, and we can implement it quickly with high quality.

After ChatGPT became popular, China successively launched products like Wenxin Yiyan, Tongyi Qianwen, and iFlytek Spark. Although model capabilities have gaps, product experiences are all quite good.

### Rich Scenarios, Abundant Data

China has 1.4 billion people with all kinds of bizarre application scenarios. This provides excellent testing grounds for AI technology implementation.

Moreover, the complexity of Chinese language brings unique challenges to AI research. Master Chinese, and other languages become relatively simple.

## So What Should We Do?

### Truly Value Fundamental Research

This topic has been discussed for years, but I think we need to keep talking about it.

Not just investing money, but more importantly, giving time and patience. Learn from Anthropic's approach: choose a direction, invest long-term, don't always think about quick monetization.

### Change Investment Mindset

Both investors and entrepreneurs need to change their thinking. Some technologies just require long-term investment—we can't always think about quick returns.

Government guidance funds can play a role here, specifically supporting projects with long-term value but no short-term profits.

### Encourage Talent Mobility

Currently, top talent stays in big tech companies, lacking mobility. We should encourage more people to start businesses or move between different institutions.

We also need to create a more open technical atmosphere, with more technical sharing and open-source projects.

### Find Ways to Solve Data Issues

While protecting privacy, explore more flexible data usage methods. Federated learning and differential privacy might be directions to pursue.

### Cooperation Where Appropriate

Although the international environment is complex now, we should still cooperate in fundamental research where appropriate. Science and technology have no borders.

## Final Thoughts

Writing this article isn't to boost others' morale while dampening our own, but because I feel some issues really need to be faced.

Claude 4 is indeed impressive, but that doesn't mean we can never catch up. The key is finding the right direction and not always sprinting down the wrong path.

I still believe that with Chinese people's intelligence and wisdom, as long as we get the approach right, creating world-class AI models is just a matter of time.

But this requires us to change some ingrained thinking:
- Stop always thinking about quick monetization; some things just take time
- Invest more in fundamental research; stop always following others
- Be more open; stop working in silos

**The gap exists objectively, but gaps aren't eternal. The key is acknowledging the gap, then finding ways to close it.**

I believe that someday we'll also create AI models that shock the world. When that time comes, we won't need to ask "Why can't we build Claude 4?" but rather "Where's the next breakthrough?"

What do you think? Can we really catch up?
