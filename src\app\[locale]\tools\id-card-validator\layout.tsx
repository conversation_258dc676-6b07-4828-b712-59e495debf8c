import { Metadata } from "next";
import { getTranslations } from 'next-intl/server';
import { SITE_NAME, SITE_URL } from "@/lib/constants";

type Props = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.tools.tools.idCardValidator' });
  const tSite = await getTranslations({ locale, namespace: 'site' });

  const getLocalizedPath = (path: string) => {
    if (locale === 'zh') {
      return path;
    }
    return `/${locale}${path}`;
  };

  const getHreflangAlternates = (basePath: string) => {
    return {
      'zh': basePath,
      'en': `/en${basePath}`,
    };
  };

  const title = `${t('meta.title')} | ${tSite('name')}`;
  const description = t('meta.description');
  const canonicalPath = getLocalizedPath('/tools/id-card-validator');

  return {
    title,
    description,
    keywords: locale === 'zh' ? [
      "身份证查询器",
      "身份证验证",
      "身份证号码验证",
      "身份证信息查询",
      "身份证解析",
      "ID Card Validator",
      "身份证校验",
      "身份证归属地",
      "在线身份证工具",
      "免费身份证验证",
      "身份证格式检查",
      "身份证真伪验证"
    ] : [
      "Chinese ID card validator",
      "ID card verification",
      "ID number validation",
      "ID card information query",
      "ID card parser",
      "Chinese identity card",
      "ID card checker",
      "ID card region lookup",
      "online ID tools",
      "free ID validation",
      "ID format check",
      "ID authenticity verification"
    ],
    authors: [{ name: tSite('author') }],
    creator: tSite('author'),
    publisher: tSite('name'),
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL(SITE_URL),
    alternates: {
      canonical: canonicalPath,
      languages: getHreflangAlternates('/tools/id-card-validator'),
    },
    openGraph: {
      title,
      description,
      url: canonicalPath,
      siteName: tSite('name'),
      locale: locale === 'zh' ? 'zh_CN' : 'en_US',
      type: "website",
      images: [
        {
          url: "/images/tools/id-card-validator-og.jpg",
          width: 1200,
          height: 630,
          alt: t('meta.title'),
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: ["/images/tools/id-card-validator-og.jpg"],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  };
}

export default function IdCardValidatorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}