{"title": "🔐 Base64 Encoder/Decoder Tool", "description": "Online Base64 encoding and decoding tool, supports text and file processing", "breadcrumb": {"home": "Home", "tools": "Toolbox", "current": "Base64 Encoder/Decoder"}, "modes": {"encode": "Encode", "decode": "Decode"}, "controls": {"mode": "Processing Mode", "encode": "Encode", "decode": "Decode", "swap": "<PERSON><PERSON><PERSON>", "clear": "Clear"}, "input": {"label": "Input Content", "placeholder": "Enter text content to process..."}, "output": {"label": "Output Result", "placeholder": "Processing result will be displayed here..."}, "actions": {"process": "Process", "clear": "Clear", "copy": "<PERSON><PERSON> Result"}, "messages": {"copySuccess": "Result copied to clipboard!", "copyFailed": "Copy failed, please copy manually", "invalidInput": "Invalid input content, please check and try again", "emptyInput": "Please enter content to process", "copied": "Copied to clipboard!"}, "errors": {"encodeFailed": "Encoding failed, please check input content", "decodeFailed": "Decoding failed, please check if Base64 format is correct"}, "sections": {"originalText": "Original Text", "base64Encode": "Base64 Encoded", "decodeResult": "Decode Result", "characters": "characters", "copy": "Copy"}, "placeholders": {"encodeInput": "Enter text content to encode...", "decodeInput": "Enter Base64 content to decode...", "encodeOutput": "Encoding result will be displayed here...", "decodeOutput": "Decoding result will be displayed here..."}, "tips": {"title": "Usage Tips", "items": {"0": "Base64 is a representation method for binary data based on 64 printable characters", "1": "Commonly used to pass long identification information in HTTP environments", "2": "Encoded data is slightly longer than original data (about 4/3 times)", "3": "Base64 encoding is reversible and can restore original data through decoding", "4": "Supports encoding and decoding of Unicode characters including Chinese"}}, "features": {"title": "Features", "encode": "Text Base64 encoding", "decode": "Base64 decoding restoration", "realtime": "Real-time processing", "copy": "One-click copy result"}, "instructions": {"title": "Instructions", "encode": "Encode: Convert plain text to Base64 format", "decode": "Decode: Restore Base64 format to plain text", "step1": "Select encode or decode mode", "step2": "Enter content to process", "step3": "Click process button or view results in real-time", "step4": "Copy processing result to clipboard"}, "meta": {"title": "Base64 Encoder/Decoder Tool - Free Online Base64 Converter", "description": "Free online Base64 encoding and decoding tool, supports Base64 encoding and decoding conversion of text, simple operation, accurate results."}}