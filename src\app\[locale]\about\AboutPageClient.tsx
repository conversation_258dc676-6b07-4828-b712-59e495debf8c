'use client';

import Image from 'next/image';

type TranslationsType = {
  title: string;
  subtitle: string;
  authorName: string;
  skills: {
    frontend: { title: string; items: string[] };
    backend: { title: string; items: string[] };
    mobile: { title: string; items: string[] };
    project: { title: string; items: string[] };
  };
  philosophy: {
    title: string;
    practical: { title: string; description: string };
    accessible: { title: string; description: string };
    learning: { title: string; description: string };
    communication: { title: string; description: string };
  };
  techStack: {
    title: string;
    frontend: { title: string };
    backend: { title: string };
    tools: { title: string };
  };
  contact: {
    title: string;
    description: string;
    sendEmail: string;
    github: string;
  };
};

type Props = {
  locale: string;
  translations: TranslationsType;
};

export default function AboutPageClient({ locale, translations: t }: Props) {
  const frontendTechs = [
    'React', 'Next.js', 'Vue.js', 'Tailwind CSS', 'JavaScript', 'H5',
    locale === 'zh' ? '微信小程序' : 'WeChat Mini Program',
    'Flutter',
    locale === 'zh' ? '企业微信' : 'Enterprise WeChat',
    'uniapp', 'Taro'
  ];

  const backendTechs = ['Node.js', 'Python', 'Java', 'Go', 'PHP', 'MongoDB', 'Redis'];

  const toolsTechs = [
    'Docker', 'Kubernetes', 'MySQL', 'PostgreSQL', 'Redis', 'Git', 'Linux', 'Nginx',
    locale === 'zh' ? '金蝶云苍穹低代码开发平台' : 'Kingdee Cloud Platform'
  ];

  return (
    <main>
      <div className="container-custom">
        <div className="max-w-4xl mx-auto py-16">
          <div className="prose prose-lg dark:prose-invert max-w-none">
            {/* 页面标题 */}
            <div className="text-center mb-12">
              <h1 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                {t.title}
              </h1>
              <p className="text-lg text-slate-600 dark:text-slate-400">
                {t.subtitle}
              </p>
            </div>

            {/* 作者介绍 */}
            <div className="relative rounded-2xl p-8 mb-12 overflow-hidden">
              {/* 背景头像 */}
               <div className="absolute inset-0 opacity-30 dark:opacity-20">
                 <Image
                   src="/images/avatar1.jpg"
                   alt={t.authorName}
                   fill
                   className="object-cover object-center"
                 />
               </div>
               {/* 渐变遮罩 */}
               <div className="absolute inset-0 bg-gradient-to-r from-blue-50/70 to-purple-50/70 dark:from-blue-900/60 dark:to-purple-900/60"></div>
              
              {/* 内容区域 */}
              <div className="relative z-10">
                <div className="text-center">
                  <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-3">
                    {t.authorName}
                  </h2>
                  <div className="text-slate-600 dark:text-slate-300 leading-relaxed space-y-6 mt-6">
                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="font-semibold text-slate-800 dark:text-slate-200 mb-3">{t.skills.frontend.title}</h3>
                          <ul className="space-y-2 text-sm">
                            {t.skills.frontend.items.map((item: string, index: number) => (
                              <li key={index}>{item}</li>
                            ))}
                          </ul>
                        </div>
                        
                        <div>
                          <h3 className="font-semibold text-slate-800 dark:text-slate-200 mb-3">{t.skills.backend.title}</h3>
                          <ul className="space-y-2 text-sm">
                            {t.skills.backend.items.map((item: string, index: number) => (
                              <li key={index}>{item}</li>
                            ))}
                          </ul>
                        </div>
                        
                        <div>
                          <h3 className="font-semibold text-slate-800 dark:text-slate-200 mb-3">{t.skills.mobile.title}</h3>
                          <ul className="space-y-2 text-sm">
                            {t.skills.mobile.items.map((item: string, index: number) => (
                              <li key={index}>{item}</li>
                            ))}
                          </ul>
                        </div>
                        
                        <div>
                          <h3 className="font-semibold text-slate-800 dark:text-slate-200 mb-3">{t.skills.project.title}</h3>
                          <ul className="space-y-2 text-sm">
                            {t.skills.project.items.map((item: string, index: number) => (
                              <li key={index}>{item}</li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                </div>
              </div>
            </div>

            {/* 博客理念 */}
            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-6">
                {t.philosophy.title}
              </h2>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-sm border border-slate-200 dark:border-slate-700">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">
                    {t.philosophy.practical.title}
                  </h3>
                  <p className="text-slate-600 dark:text-slate-300 text-sm">
                    {t.philosophy.practical.description}
                  </p>
                </div>
                
                <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-sm border border-slate-200 dark:border-slate-700">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">
                    {t.philosophy.accessible.title}
                  </h3>
                  <p className="text-slate-600 dark:text-slate-300 text-sm">
                    {t.philosophy.accessible.description}
                  </p>
                </div>
                
                <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-sm border border-slate-200 dark:border-slate-700">
                  <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">
                    {t.philosophy.learning.title}
                  </h3>
                  <p className="text-slate-600 dark:text-slate-300 text-sm">
                    {t.philosophy.learning.description}
                  </p>
                </div>
                
                <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-sm border border-slate-200 dark:border-slate-700">
                  <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">
                    {t.philosophy.communication.title}
                  </h3>
                  <p className="text-slate-600 dark:text-slate-300 text-sm">
                    {t.philosophy.communication.description}
                  </p>
                </div>
              </div>
            </section>

            {/* 技术栈 */}
            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-6">
                {t.techStack.title}
              </h2>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-slate-900 dark:text-white mb-3">{t.techStack.frontend.title}</h3>
                  <div className="flex flex-wrap gap-2">
                    {frontendTechs.map((tech: string) => (
                      <span key={tech} className="px-3 py-1 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-lg text-sm border border-blue-200 dark:border-blue-800">
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium text-slate-900 dark:text-white mb-3">{t.techStack.backend.title}</h3>
                  <div className="flex flex-wrap gap-2">
                    {backendTechs.map((tech: string) => (
                      <span key={tech} className="px-3 py-1 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-lg text-sm border border-green-200 dark:border-green-800">
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium text-slate-900 dark:text-white mb-3">{t.techStack.tools.title}</h3>
                  <div className="flex flex-wrap gap-2">
                    {toolsTechs.map((tech: string) => (
                      <span key={tech} className="px-3 py-1 bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 rounded-lg text-sm border border-purple-200 dark:border-purple-800">
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </section>

            {/* 联系方式 */}
            <section className="bg-slate-50 dark:bg-slate-800/50 rounded-2xl p-8 text-center">
              <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                {t.contact.title}
              </h2>
              <p className="text-slate-600 dark:text-slate-300 mb-6">
                {t.contact.description}
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-300"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  {t.contact.sendEmail}
                </a>
                <a
                  href="https://github.com/pythonsir"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-2 px-6 py-3 bg-slate-800 hover:bg-slate-900 text-white rounded-lg transition-colors duration-300"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                  </svg>
                  {t.contact.github}
                </a>
              </div>
            </section>
          </div>
        </div>
      </div>
    </main>
  );
}