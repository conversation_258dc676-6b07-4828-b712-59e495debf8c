import Image from "next/image";
import Link from "next/link";
import { type Author } from "@/interfaces/author";
import LocalizedDateFormatter from "./localized-date-formatter";
import { ReadingTime } from "./reading-time";

type Post = {
  slug: string;
  title: string;
  date: string;
  coverImage: string;
  author: Author;
  excerpt: string;
  content: string;
};

type Props = {
  post: Post;
  translations: {
    badge: string;
    title: string;
    imageBadge: string;
    readFull: string;
    author: string;
    authortag: string;
  };
  locale: string;
};

export function FeaturedArticle({ post, translations, locale }: Props) {
  const { defaultLocale } = require('@/i18n');
  return (
    <section id="featured" className="py-8 md:py-0 relative">
      {/* 背景装饰 */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute top-1/2 left-1/4 w-32 h-32 bg-gradient-to-br from-blue-200/30 to-purple-200/30 dark:from-blue-800/20 dark:to-purple-800/20 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute top-1/3 right-1/3 w-24 h-24 bg-gradient-to-br from-pink-200/30 to-orange-200/30 dark:from-pink-800/20 dark:to-orange-800/20 rounded-full blur-3xl animate-pulse-slow animation-delay-2000"></div>
      </div>

      <div className="mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* 标题区域 */}
          <div className="text-center mb-8">
            <span className="inline-block px-2.5 py-1 bg-gradient-to-r from-yellow-100 to-orange-100 dark:from-yellow-900/30 dark:to-orange-900/30 text-yellow-700 dark:text-yellow-300 rounded-full text-sm font-medium mb-3">
              {translations.badge}
            </span>
            <h2 className="text-xl md:text-2xl font-bold text-slate-900 dark:text-white">
              {translations.title}
            </h2>
          </div>

          {/* 特色文章卡片 */}
          <div className="relative rounded-2xl transition-all duration-500 group shadow-md hover:shadow-lg">
            {/* 外层发光效果 */}
            <div className="absolute -inset-px bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-2xl opacity-0 group-hover:opacity-10 blur-sm transition-all duration-500 -z-10"></div>

            {/* 内层卡片 */}
            <div className="relative bg-white dark:bg-slate-800 rounded-2xl overflow-hidden border border-slate-200/50 dark:border-slate-700/50">
              <div className="grid lg:grid-cols-2 gap-0">
                {/* 左侧：封面图片 */}
                <div className="relative h-48 lg:h-full overflow-hidden">
                  <Image
                    src={post.coverImage}
                    alt={post.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-500"
                    priority
                  />

                  {/* 图片遮罩层 */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                  {/* 特色标签 */}
                  <div className="absolute top-4 left-4">
                    <span className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-1.5 rounded-full text-xs font-semibold shadow-lg backdrop-blur-sm border border-white/20">
                      {translations.imageBadge}
                    </span>
                  </div>
                  {/* 悬浮阅读按钮 */}
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500 bg-black/20 backdrop-blur-sm">
                    <Link
                      href={`/posts/${post.slug}`}
                      className="bg-white/95 backdrop-blur-sm text-slate-900 px-6 py-3 rounded-full text-sm font-semibold hover:bg-white transition-all duration-300 shadow-2xl hover:shadow-3xl hover:scale-105 border border-white/50"
                    >
                      <span className="flex items-center gap-2">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        {translations.readFull}
                      </span>
                    </Link>
                  </div>
                </div>

                {/* 右侧内容 */}
                <div className="p-6 lg:p-8 flex flex-col justify-center relative">
                  {/* 内容区域背景装饰 */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-transparent to-purple-50/50 dark:from-blue-900/10 dark:via-transparent dark:to-purple-900/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                  <div className="relative z-10">
                    {/* 文章元信息 */}
                    <div className="flex items-center gap-3 mb-4">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full shadow-sm"></div>
                        <span className="text-xs text-slate-500 dark:text-slate-400 font-medium">
                          <LocalizedDateFormatter dateString={post.date} />
                        </span>
                      </div>

                      <div className="flex items-center gap-2">
                        <svg className="w-3 h-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <ReadingTime
                          content={post.content}
                          format="full"
                          className="text-xs text-slate-500 dark:text-slate-400"
                        />
                      </div>
                    </div>

                    {/* 标题 */}
                    <h3 className="text-xl lg:text-2xl font-bold leading-tight mb-3 text-slate-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                      <Link href={`/posts/${post.slug}`} className="hover:drop-shadow-sm">
                        {post.title}
                      </Link>
                    </h3>

                    {/* 摘要 */}
                    <p className="text-base text-slate-600 dark:text-slate-300 leading-relaxed mb-6 line-clamp-3">
                      {post.excerpt}
                    </p>

                    {/* 底部信息 */}
                    <div className="flex items-center justify-between">
                      {/* 作者信息 */}
                      <div className="flex items-center gap-3">
                        <div className="relative">
                          <Image
                            src="/images/avatar1.jpg"
                            alt={post.author.name}
                            width={40}
                            height={40}
                            className="rounded-full shadow-md ring-2 ring-white/50 dark:ring-slate-700/50"
                          />
                          <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-slate-800 shadow-sm"></div>
                        </div>
                        <div>
                          <p className="text-sm font-semibold text-slate-900 dark:text-white">
                            {post.author.name}
                          </p>
                          <p className="text-xs text-slate-600 dark:text-slate-400">
                            {translations.authortag}
                          </p>
                        </div>
                      </div>

                      {/* 桌面端阅读按钮 */}
                      <div className="hidden lg:block">
                        <Link
                          href={locale === defaultLocale ? `/posts/${post.slug}` : `/${locale}/posts/${post.slug}`}
                          className="inline-flex items-center gap-2 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-6 py-3 rounded-full text-sm font-semibold transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-purple-500/25 hover:scale-105 group/btn"
                        >
                          {translations.readFull}
                          <svg className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 底部装饰线 */}
              <div className="h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left shadow-sm"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
