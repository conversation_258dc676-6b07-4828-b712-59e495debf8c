import { useRef } from 'react';

import { useTranslations } from 'next-intl';

interface QRPreviewProps {
  qrCodeDataUrl: string | null;
  qrSize: number;
  isGenerating: boolean;
  onDownload: (format: 'png' | 'svg') => void;
  onCopyImage: () => void;
  generateContent: () => string;
}

export default function QRPreview({
  qrCodeDataUrl,
  qrSize,
  isGenerating,
  onDownload,
  onCopyImage,
  generateContent
}: QRPreviewProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const t = useTranslations('pages.tools.tools.qrGenerator');

  return (
    <div className="space-y-6">
      {/* 二维码预览 */}
      <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
          {t('preview.title')}
        </h2>
        <div className="flex flex-col items-center space-y-4">
          <div className="p-4 bg-gray-50 dark:bg-slate-700 rounded-lg">
            {qrCodeDataUrl ? (
              <img
                src={qrCodeDataUrl}
                alt="Generated QR Code"
                className="max-w-full h-auto"
                style={{ width: qrSize, height: qrSize }}
              />
            ) : (
              <div 
                className="flex items-center justify-center bg-gray-200 dark:bg-slate-600 rounded-lg"
                style={{ width: qrSize, height: qrSize }}
              >
                <span className="text-gray-500 dark:text-gray-400">
                  {isGenerating ? t('status.generating') : t('status.enterContent')}
                </span>
              </div>
            )}
          </div>

          {/* 操作按钮 */}
          {qrCodeDataUrl && (
            <div className="flex flex-wrap gap-3 justify-center">
              <button
                onClick={() => onDownload('png')}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center space-x-2"
              >
                <span>📥</span>
                <span>{t('actions.downloadPNG')}</span>
              </button>
              <button
                onClick={() => onDownload('svg')}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center space-x-2"
              >
                <span>📄</span>
                <span>{t('actions.downloadSVG')}</span>
              </button>
              <button
                onClick={onCopyImage}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 flex items-center space-x-2"
              >
                <span>📋</span>
                <span>{t('actions.copyImage')}</span>
              </button>
            </div>
          )}
        </div>
        <canvas
          ref={canvasRef}
          style={{ display: 'none' }}
        />
      </div>

      {/* 数据统计 */}
      <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
          {t('stats.title')}
        </h2>
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {qrCodeDataUrl ? 1 : 0}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">{t('stats.generated')}</div>
          </div>
          <div className="text-center p-4 bg-gradient-to-r from-green-50 to-teal-50 dark:from-green-900/20 dark:to-teal-900/20 rounded-lg">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {generateContent().length}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">{t('stats.characters')}</div>
          </div>
        </div>
      </div>

      {/* 使用说明 */}
      <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
          {t('instructions.title')}
        </h2>
        <div className="space-y-3 text-sm text-gray-600 dark:text-gray-300">
          <div className="flex items-start space-x-2">
            <span className="text-purple-500 font-bold">1.</span>
            <span>{t('instructions.step1')}</span>
          </div>
          <div className="flex items-start space-x-2">
            <span className="text-purple-500 font-bold">2.</span>
            <span>{t('instructions.step2')}</span>
          </div>
          <div className="flex items-start space-x-2">
            <span className="text-purple-500 font-bold">3.</span>
            <span>{t('instructions.step3')}</span>
          </div>
          <div className="flex items-start space-x-2">
            <span className="text-purple-500 font-bold">4.</span>
            <span>{t('instructions.step4')}</span>
          </div>
        </div>

        <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <h3 className="font-semibold text-blue-900 dark:text-blue-300 mb-2">
            💡 {t('tips.title')}
          </h3>
          <ul className="text-sm text-blue-800 dark:text-blue-300 space-y-1">
            <li>• {t('tips.errorLevel')}</li>
            <li>• {t('tips.wifi')}</li>
            <li>• {t('tips.vcard')}</li>
            <li>• {t('tips.contrast')}</li>
          </ul>
        </div>
      </div>
    </div>
  );
}