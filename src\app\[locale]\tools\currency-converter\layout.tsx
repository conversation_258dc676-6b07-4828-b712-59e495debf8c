import { Metadata } from "next";
import { getTranslations } from 'next-intl/server';
import { SITE_NAME, SITE_URL } from "@/lib/constants";

type Props = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'pages.tools.tools.currencyConverter' });
  const tSite = await getTranslations({ locale, namespace: 'site' });

  const getLocalizedPath = (path: string) => {
    if (locale === 'zh') {
      return path;
    }
    return `/${locale}${path}`;
  };

  const title = `${t('meta.title')} | ${tSite('name')}`;
  const description = t('meta.description');
  const canonicalPath = getLocalizedPath('/tools/currency-converter');

  return {
    title,
    description,
    keywords: locale === 'zh' ? [
      "汇率转换器",
      "货币转换",
      "实时汇率",
      "汇率查询",
      "外汇汇率",
      "美元汇率",
      "欧元汇率",
      "英镑汇率",
      "日元汇率",
      "人民币汇率",
      "在线汇率工具",
      "免费汇率转换",
      "外币兑换",
      "汇率计算器"
    ] : [
      "currency converter",
      "exchange rates",
      "real-time rates",
      "currency exchange",
      "forex rates",
      "USD rates",
      "EUR rates",
      "GBP rates",
      "JPY rates",
      "CNY rates",
      "online currency tool",
      "free currency converter",
      "foreign exchange",
      "rate calculator"
    ],
    authors: [{ name: tSite('author') }],
    creator: tSite('author'),
    publisher: tSite('name'),
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL(SITE_URL),
    alternates: {
      canonical: canonicalPath,
      languages: {
        'zh': '/tools/currency-converter',
        'en': '/en/tools/currency-converter',
      },
    },
    openGraph: {
      title,
      description,
      url: canonicalPath,
      siteName: tSite('name'),
      locale: locale === 'zh' ? 'zh_CN' : 'en_US',
      type: "website",
      images: [
        {
          url: "/images/tools/currency-converter-og.jpg",
          width: 1200,
          height: 630,
          alt: t('meta.title'),
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: ["/images/tools/currency-converter-og.jpg"],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  };
}

export default function CurrencyConverterLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}