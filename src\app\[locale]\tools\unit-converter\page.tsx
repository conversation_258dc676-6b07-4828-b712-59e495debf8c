'use client'

import { useState, useCallback, useMemo } from 'react'
import Link from 'next/link'
import { useLocale, useTranslations } from 'next-intl'
import { defaultLocale } from '@/i18n'
import { Calculator, ArrowLeftRight, Copy } from 'lucide-react'
import { SITE_NAME, SITE_URL } from '@/lib/constants'

// 单位转换配置
const UNIT_CATEGORIES = {
  length: {
    name: '长度',
    icon: '📏',
    units: {
      mm: { name: '毫米', factor: 1 },
      cm: { name: '厘米', factor: 10 },
      m: { name: '米', factor: 1000 },
      km: { name: '千米', factor: 1000000 },
      inch: { name: '英寸', factor: 25.4 },
      ft: { name: '英尺', factor: 304.8 },
      yard: { name: '码', factor: 914.4 },
      mile: { name: '英里', factor: 1609344 }
    }
  },
  weight: {
    name: '重量',
    icon: '⚖️',
    units: {
      mg: { name: '毫克', factor: 1 },
      g: { name: '克', factor: 1000 },
      kg: { name: '千克', factor: 1000000 },
      t: { name: '吨', factor: 1000000000 },
      oz: { name: '盎司', factor: 28349.5 },
      lb: { name: '磅', factor: 453592 }
    }
  },
  area: {
    name: '面积',
    icon: '📐',
    units: {
      mm2: { name: '平方毫米', factor: 1 },
      cm2: { name: '平方厘米', factor: 100 },
      m2: { name: '平方米', factor: 1000000 },
      km2: { name: '平方千米', factor: 1000000000000 },
      acre: { name: '英亩', factor: 4046856422.4 },
      hectare: { name: '公顷', factor: 10000000000 }
    }
  },
  volume: {
    name: '体积',
    icon: '🧊',
    units: {
      ml: { name: '毫升', factor: 1 },
      l: { name: '升', factor: 1000 },
      m3: { name: '立方米', factor: 1000000 },
      gallon: { name: '加仑(美)', factor: 3785.41 },
      quart: { name: '夸脱', factor: 946.353 },
      pint: { name: '品脱', factor: 473.176 }
    }
  },
  temperature: {
    name: '温度',
    icon: '🌡️',
    units: {
      celsius: { name: '摄氏度', factor: 1 },
      fahrenheit: { name: '华氏度', factor: 1 },
      kelvin: { name: '开尔文', factor: 1 }
    }
  },
  speed: {
    name: '速度',
    icon: '🚗',
    units: {
      mps: { name: '米/秒', factor: 1 },
      kmh: { name: '千米/时', factor: 0.277778 },
      mph: { name: '英里/时', factor: 0.44704 },
      knot: { name: '节', factor: 0.514444 }
    }
  }
}

export default function UnitConverterPage() {
  const [category, setCategory] = useState('length')
  const [fromUnit, setFromUnit] = useState('m')
  const [toUnit, setToUnit] = useState('cm')
  const [inputValue, setInputValue] = useState('')
  const [result, setResult] = useState('')

  const locale = useLocale()
  const isEnglish = locale === 'en'
  const t = useTranslations('pages.tools.tools.unitConverter')

  // 根据当前语言生成正确的链接
  const getLocalizedHref = (path: string) => {
    if (locale === defaultLocale) {
      return path;
    }
    return `/${locale}${path}`;
  };

  // 获取当前分类的单位
  const currentUnits = useMemo(() => {
    return UNIT_CATEGORIES[category as keyof typeof UNIT_CATEGORIES]?.units || {}
  }, [category])

  // 复制到剪贴板
  const copyToClipboard = useCallback(async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
    } catch {
      console.error('复制失败')
    }
  }, [])

  // 温度转换特殊处理
  const convertTemperature = useCallback((value: number, from: string, to: string): number => {
    let celsius: number
    
    // 转换为摄氏度
    switch (from) {
      case 'celsius':
        celsius = value
        break
      case 'fahrenheit':
        celsius = (value - 32) * 5 / 9
        break
      case 'kelvin':
        celsius = value - 273.15
        break
      default:
        celsius = value
    }
    
    // 从摄氏度转换为目标单位
    switch (to) {
      case 'celsius':
        return celsius
      case 'fahrenheit':
        return celsius * 9 / 5 + 32
      case 'kelvin':
        return celsius + 273.15
      default:
        return celsius
    }
  }, [])

  // 单位转换
  const convertUnit = useCallback((value: number, from: string, to: string, cat: string): number => {
    if (cat === 'temperature') {
      return convertTemperature(value, from, to)
    }
    
    const categoryData = UNIT_CATEGORIES[cat as keyof typeof UNIT_CATEGORIES]
    if (!categoryData) {
      return value
    }
    
    const units = categoryData.units as Record<string, { name: string; factor: number }>
    if (!units[from] || !units[to]) {
      return value
    }
    
    // 转换为基准单位，再转换为目标单位
    const baseValue = value * units[from].factor
    return baseValue / units[to].factor
  }, [convertTemperature])

  // 处理转换
  const handleConvert = useCallback(() => {
    if (!inputValue.trim()) {
      setResult('')
      return
    }
    
    const num = parseFloat(inputValue)
    if (isNaN(num)) {
      setResult('请输入有效数字')
      return
    }
    
    try {
      const convertedValue = convertUnit(num, fromUnit, toUnit, category)
      
      // 格式化结果
      let formattedResult: string
      if (Math.abs(convertedValue) >= 1000000) {
        formattedResult = convertedValue.toExponential(6)
      } else if (Math.abs(convertedValue) < 0.000001 && convertedValue !== 0) {
        formattedResult = convertedValue.toExponential(6)
      } else {
        formattedResult = parseFloat(convertedValue.toFixed(10)).toString()
      }
      
      setResult(formattedResult)
    } catch {
      setResult('转换失败')
    }
  }, [inputValue, fromUnit, toUnit, category, convertUnit])

  // 当分类改变时重置单位选择
  const handleCategoryChange = (newCategory: string) => {
    setCategory(newCategory)
    const units = UNIT_CATEGORIES[newCategory as keyof typeof UNIT_CATEGORIES]?.units
    if (units) {
      const unitKeys = Object.keys(units)
      setFromUnit(unitKeys[0] || '')
      setToUnit(unitKeys[1] || unitKeys[0] || '')
    }
    setResult('')
  }

  // 交换单位
  const swapUnits = () => {
    const temp = fromUnit
    setFromUnit(toUnit)
    setToUnit(temp)
    if (result && !isNaN(parseFloat(result))) {
      setInputValue(result)
      const convertedValue = convertUnit(parseFloat(result), toUnit, temp, category)
      setResult(parseFloat(convertedValue.toFixed(10)).toString())
    }
  }

  // 常用转换示例
  const getExamples = () => {
    switch (category) {
      case 'length':
        return [
          { from: '1', fromUnit: 'm', toUnit: 'cm', description: '1米 = 100厘米' },
          { from: '1', fromUnit: 'km', toUnit: 'm', description: '1千米 = 1000米' },
          { from: '1', fromUnit: 'inch', toUnit: 'cm', description: '1英寸 = 2.54厘米' }
        ]
      case 'weight':
        return [
          { from: '1', fromUnit: 'kg', toUnit: 'g', description: '1千克 = 1000克' },
          { from: '1', fromUnit: 'lb', toUnit: 'kg', description: '1磅 ≈ 0.454千克' },
          { from: '1', fromUnit: 't', toUnit: 'kg', description: '1吨 = 1000千克' }
        ]
      case 'temperature':
        return [
          { from: '0', fromUnit: 'celsius', toUnit: 'fahrenheit', description: '0°C = 32°F' },
          { from: '100', fromUnit: 'celsius', toUnit: 'fahrenheit', description: '100°C = 212°F' },
          { from: '273.15', fromUnit: 'kelvin', toUnit: 'celsius', description: '273.15K = 0°C' }
        ]
      default:
        return []
    }
  }

  const handleExampleClick = (example: { from: string; fromUnit: string; toUnit: string }) => {
    setFromUnit(example.fromUnit)
    setToUnit(example.toUnit)
    setInputValue(example.from)
    const convertedValue = convertUnit(parseFloat(example.from), example.fromUnit, example.toUnit, category)
    setResult(parseFloat(convertedValue.toFixed(10)).toString())
  }

  // 如果是英文环境，显示说明页面
  if (isEnglish) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="container-custom mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Breadcrumb */}
          <nav className="flex mb-8" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <Link href={getLocalizedHref('/')} className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                  <svg className="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                  </svg>
                  {t('breadcrumb.home')}
                </Link>
              </li>
              <li>
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                  <Link href={getLocalizedHref('/tools')} className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">{t('breadcrumb.tools')}</Link>
                </div>
              </li>
              <li aria-current="page">
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                  </svg>
                  <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">{t('breadcrumb.current')}</span>
                </div>
              </li>
            </ol>
          </nav>

          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              📐 Unit Converter Tool
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
              Convert between different units of measurement
            </p>

            <div className="max-w-2xl mx-auto bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-blue-800 dark:text-blue-200 mb-4">
                🇨🇳 Chinese Interface Available
              </h2>
              <p className="text-blue-700 dark:text-blue-300 mb-4">
                This unit converter tool is currently available with a Chinese interface.
                It supports conversion between various units including length, weight, area, volume, temperature, and speed.
              </p>
              <p className="text-blue-700 dark:text-blue-300 mb-6">
                <strong>Supported categories:</strong> Length (米, 厘米, 英寸, 英尺), Weight (克, 千克, 磅),
                Area (平方米, 平方厘米), Volume (升, 毫升, 加仑), Temperature (摄氏度, 华氏度), Speed (米/秒, 千米/时)
              </p>

              <div className="flex justify-center">
                <Link
                  href="/tools/unit-converter"
                  className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Calculator className="w-5 h-5 mr-2" />
                  Use Tool (Chinese Interface)
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
      {/* JSON-LD 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": "计量单位转换工具",
            "description": "免费的在线计量单位转换工具，支持长度、重量、面积、体积、温度、速度等多种单位的精确转换。",
            "url": `${SITE_URL}/tools/unit-converter`,
            "applicationCategory": "UtilitiesApplication",
            "operatingSystem": "Any",
            "permissions": "browser",
            "isAccessibleForFree": true,
            "creator": {
              "@type": "Organization",
              "name": SITE_NAME
            },
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "CNY"
            }
          })
        }}
      />
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <div className="container-custom py-20">
        {/* 面包屑导航 */}
        <nav className="flex mb-8" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <Link href="/" className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                <svg className="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                  <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                </svg>
                首页
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                  <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                </svg>
                <Link href="/tools" className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">
                  工具箱
                </Link>
              </div>
            </li>
            <li aria-current="page">
              <div className="flex items-center">
                <svg className="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                  <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                </svg>
                <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">计量单位转换</span>
              </div>
            </li>
          </ol>
        </nav>

        {/* 页面标题 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            📏 计量单位转换工具
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            支持长度、重量、面积、体积、温度、速度等多种单位的精确转换
          </p>
        </div>

        {/* 主要内容 */}
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-3 gap-6">
            {/* 左侧：转换器 */}
            <div className="lg:col-span-2 space-y-6">
              {/* 分类选择 */}
              <div className="bg-white rounded-lg border shadow-sm">
                <div className="p-6 pb-4">
                  <h3 className="text-lg font-semibold mb-4">选择转换类型</h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {Object.entries(UNIT_CATEGORIES).map(([key, cat]) => (
                      <button
                        key={key}
                        onClick={() => handleCategoryChange(key)}
                        className={`p-3 rounded-lg border text-left transition-colors ${
                          category === key
                            ? 'bg-purple-50 border-purple-200 text-purple-700'
                            : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                        }`}
                      >
                        <div className="text-lg mb-1">{cat.icon}</div>
                        <div className="font-medium text-sm">{cat.name}</div>
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* 转换器 */}
              <div className="bg-white rounded-lg border shadow-sm">
                <div className="p-6 pb-4">
                  <h3 className="text-lg font-semibold flex items-center gap-2">
                    <Calculator className="h-5 w-5" />
                    单位转换
                  </h3>
                </div>
                <div className="px-6 pb-6 space-y-4">
                  {/* 输入区域 */}
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">从</label>
                      <div className="flex gap-2">
                        <input
                          type="number"
                          placeholder="输入数值"
                          value={inputValue}
                          onChange={(e) => setInputValue(e.target.value)}
                          onKeyDown={(e) => e.key === 'Enter' && handleConvert()}
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        />
                        <select
                          value={fromUnit}
                          onChange={(e) => setFromUnit(e.target.value)}
                          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        >
                          {Object.entries(currentUnits).map(([key, unit]) => (
                            <option key={key} value={key}>{unit.name}</option>
                          ))}
                        </select>
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">到</label>
                      <div className="flex gap-2">
                        <input
                          type="text"
                          value={result}
                          readOnly
                          placeholder="转换结果"
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
                        />
                        <select
                          value={toUnit}
                          onChange={(e) => setToUnit(e.target.value)}
                          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        >
                          {Object.entries(currentUnits).map(([key, unit]) => (
                            <option key={key} value={key}>{unit.name}</option>
                          ))}
                        </select>
                      </div>
                    </div>
                  </div>
                  
                  {/* 操作按钮 */}
                  <div className="flex gap-2">
                    <button
                      onClick={handleConvert}
                      className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                    >
                      转换
                    </button>
                    <button
                      onClick={swapUnits}
                      className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 flex items-center gap-2"
                    >
                      <ArrowLeftRight className="h-4 w-4" />
                      交换
                    </button>
                    {result && (
                      <button
                        onClick={() => copyToClipboard(result)}
                        className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center gap-2"
                      >
                        <Copy className="h-4 w-4" />
                        复制
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* 右侧：示例和说明 */}
            <div className="space-y-6">
              {/* 常用转换 */}
              <div className="bg-white rounded-lg border shadow-sm">
                <div className="p-6 pb-4">
                  <h3 className="text-lg font-semibold">常用转换</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    点击快速转换
                  </p>
                </div>
                <div className="px-6 pb-6">
                  <div className="space-y-2">
                    {getExamples().map((example, index) => (
                      <div
                        key={index}
                        className="p-3 border rounded cursor-pointer hover:bg-gray-50 transition-colors"
                        onClick={() => handleExampleClick(example)}
                      >
                        <div className="text-xs text-gray-600">
                          {example.description}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* 支持的单位 */}
              <div className="bg-white rounded-lg border shadow-sm">
                <div className="p-6 pb-4">
                  <h3 className="text-lg font-semibold">支持的单位类型</h3>
                </div>
                <div className="px-6 pb-6">
                  <div className="space-y-3 text-sm">
                    {Object.entries(UNIT_CATEGORIES).map(([key, cat]) => (
                      <div key={key}>
                        <div className="font-medium flex items-center gap-2">
                          <span>{cat.icon}</span>
                          {cat.name}
                        </div>
                        <div className="text-gray-600 text-xs mt-1">
                          {Object.values(cat.units).map(unit => unit.name).join('、')}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </>
  )
}