'use client';

type TranslationsType = {
  title: string;
  lastUpdated: string;
  sections: {
    adDisplay: {
      title: string;
      description: string;
    };
    googleAdsense: {
      title: string;
      description: string;
      items: string[];
    };
    personalizedAds: {
      title: string;
      description: string;
      items: string[];
    };
    adPreferences: {
      title: string;
      description: string;
      items: string[];
    };
    thirdPartyNetworks: {
      title: string;
      description: string;
      items: string[];
    };
    adContentResponsibility: {
      title: string;
      description: string;
      items: string[];
    };
    childrenPrivacy: {
      title: string;
      description: string;
      items: string[];
    };
    contact: {
      title: string;
      description: string;
      items: {
        email: string;
        website: string;
      };
    };
  };
  reminder: string;
  siteName: string;
};

type Props = {
  locale: string;
  translations: TranslationsType;
};

export default function AdsPolicyPageClient({ locale, translations: t }: Props) {
  return (
    <main>
      <div className="container-custom">
        <div className="max-w-4xl mx-auto py-16">
          <div className="prose prose-lg dark:prose-invert max-w-none">
            <h1 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-8">
              {t.title}
            </h1>
            
            <div className="text-sm text-slate-600 dark:text-slate-400 mb-8">
              {t.lastUpdated}：{new Date().toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US')}
            </div>

            <div className="space-y-8">
              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.adDisplay.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>
                    {t.siteName}{t.sections.adDisplay.description}
                  </p>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.googleAdsense.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>{t.sections.googleAdsense.description}</p>
                  <ul className="list-disc pl-6 space-y-2">
                    {t.sections.googleAdsense.items.map((item: string, index: number) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.personalizedAds.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>{t.sections.personalizedAds.description}</p>
                  <ul className="list-disc pl-6 space-y-2">
                    {t.sections.personalizedAds.items.map((item: string, index: number) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.adPreferences.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>{t.sections.adPreferences.description}</p>
                  <ul className="list-disc pl-6 space-y-2">
                    {t.sections.adPreferences.items.map((item: string, index: number) => (
                      <li key={index}>
                        {index === 0 ? (
                          <>
                            <strong>{locale === 'zh' ? 'Google广告设置' : 'Google Ad Settings'}</strong>：
                            {locale === 'zh' ? '访问 ' : 'Visit '}
                            <a href="https://www.google.com/settings/ads" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline ml-1">
                              www.google.com/settings/ads
                            </a>
                            {locale === 'zh' ? ' 来管理个性化广告' : ' to manage personalized ads'}
                          </>
                        ) : index === 1 ? (
                          <>
                            <strong>{locale === 'zh' ? '退出个性化广告' : 'Opt out of personalized ads'}</strong>：
                            {locale === 'zh' ? '访问 ' : 'Visit '}
                            <a href="http://www.aboutads.info/choices/" target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline ml-1">
                              aboutads.info/choices
                            </a>
                          </>
                        ) : index === 2 ? (
                          <>
                            <strong>{locale === 'zh' ? '浏览器设置' : 'Browser settings'}</strong>：
                            {locale === 'zh' ? '在浏览器中禁用Cookies或使用隐私模式' : 'Disable Cookies in your browser or use private mode'}
                          </>
                        ) : (
                          <>
                            <strong>{locale === 'zh' ? '广告拦截器' : 'Ad blockers'}</strong>：
                            {locale === 'zh' ? '使用广告拦截软件（可能影响网站功能）' : 'Use ad blocking software (may affect website functionality)'}
                          </>
                        )}
                      </li>
                    ))}
                  </ul>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.thirdPartyNetworks.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>{t.sections.thirdPartyNetworks.description}</p>
                  <ul className="list-disc pl-6 space-y-2">
                    {t.sections.thirdPartyNetworks.items.map((item: string, index: number) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.adContentResponsibility.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>{t.sections.adContentResponsibility.description}</p>
                  <ul className="list-disc pl-6 space-y-2">
                    {t.sections.adContentResponsibility.items.map((item: string, index: number) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.childrenPrivacy.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>{t.sections.childrenPrivacy.description}</p>
                  <ul className="list-disc pl-6 space-y-2">
                    {t.sections.childrenPrivacy.items.map((item: string, index: number) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-semibold text-slate-900 dark:text-white mb-4">
                  {t.sections.contact.title}
                </h2>
                <div className="space-y-4 text-slate-700 dark:text-slate-300">
                  <p>{t.sections.contact.description}</p>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>{t.sections.contact.items.email}</li>
                    <li>
                      {t.sections.contact.items.website}
                    
                    </li>
                  </ul>
                </div>
              </section>
            </div>

            <div className="mt-12 p-6 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                <strong>{locale === 'zh' ? '重要提醒：' : 'Important Reminder:'}</strong>{t.reminder}
              </p>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}