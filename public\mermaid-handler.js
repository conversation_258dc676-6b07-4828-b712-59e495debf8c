// 全局mermaid处理器
(function() {
  let mermaidInitialized = false;
  let processedElements = new Set();

  function initMermaid() {
    if (mermaidInitialized) return;

    // 动态加载mermaid
    import('https://cdn.jsdelivr.net/npm/mermaid@11.8.1/dist/mermaid.esm.min.mjs')
      .then(({ default: mermaid }) => {
        mermaid.initialize({
          startOnLoad: false,
          theme: 'default',
          securityLevel: 'loose',
          fontFamily: 'ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif',
          fontSize: 14,
          flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'basis'
          },
          pie: {
            useMaxWidth: true
          },
          sequence: {
            useMaxWidth: true,
            diagramMarginX: 50,
            diagramMarginY: 10
          },
          class: {
            useMaxWidth: true
          },
          gantt: {
            useMaxWidth: true
          }
        });

        function renderMermaidCharts() {
          // 清除已存在的渲染图表
          const existingContainers = document.querySelectorAll('[data-mermaid-rendered="true"]');
          existingContainers.forEach(container => container.remove());

          // 获取所有mermaid代码块
          const allMermaidBlocks = document.querySelectorAll('pre code.language-mermaid');
          
          allMermaidBlocks.forEach(async (block, i) => {
            const preElement = block.closest('pre');
            
            if (!preElement || processedElements.has(preElement)) {
              return;
            }

            const chart = block.textContent || '';
            if (!chart.trim()) {
              processedElements.add(preElement);
              return;
            }

            try {
              // 立即隐藏原始代码块
              preElement.style.display = 'none';
              preElement.style.visibility = 'hidden';
              preElement.style.height = '0';
              preElement.style.overflow = 'hidden';
              preElement.style.margin = '0';
              preElement.style.padding = '0';
              preElement.classList.add('mermaid-processed');

              // 创建容器
              const container = document.createElement('div');
              container.className = 'mermaid-container my-6 flex justify-center overflow-x-auto p-4 bg-white border border-gray-200 rounded-lg';
              container.setAttribute('data-mermaid-rendered', 'true');

              // 生成唯一ID
              const chartId = `mermaid-global-${Date.now()}-${i}-${Math.random().toString(36).substr(2, 9)}`;

              // 验证语法并渲染
              const isValid = await mermaid.parse(chart);
              if (isValid) {
                // 渲染图表
                const { svg } = await mermaid.render(chartId, chart);
                container.innerHTML = svg;

                // 添加响应式样式
                const svgElement = container.querySelector('svg');
                if (svgElement) {
                  svgElement.style.maxWidth = '100%';
                  svgElement.style.height = 'auto';
                  svgElement.style.display = 'block';
                  svgElement.style.margin = '0 auto';
                }

                // 在原始代码块之后插入渲染的图表
                preElement.parentNode?.insertBefore(container, preElement.nextSibling);
              }

              // 标记为已处理
              processedElements.add(preElement);
              
            } catch (error) {
              console.error('Mermaid rendering error:', error);
              processedElements.add(preElement);
            }
          });
        }

        // 延迟执行
        setTimeout(renderMermaidCharts, 200);
        
        mermaidInitialized = true;
      })
      .catch(error => {
        console.error('Failed to load mermaid:', error);
      });
  }

  // 当DOM加载完成时初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initMermaid);
  } else {
    initMermaid();
  }

  // 导出到全局
  window.initMermaid = initMermaid;
})();
