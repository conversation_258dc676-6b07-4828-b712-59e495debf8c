export function PostSkeleton() {
  return (
    <div className="animate-pulse">
      <article className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-2xl border border-slate-200/50 dark:border-slate-700/50 p-6 shadow-lg">
        <div className="flex flex-col lg:flex-row gap-6">
          {/* 封面图片骨架 */}
          <div className="lg:w-80 flex-shrink-0">
            <div className="aspect-video bg-gradient-to-br from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-xl"></div>
          </div>

          {/* 内容区域骨架 */}
          <div className="flex-1 flex flex-col justify-between">
            <div>
              {/* 元信息骨架 */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-4">
                  <div className="w-2 h-2 bg-slate-300 dark:bg-slate-600 rounded-full"></div>
                  <div className="w-24 h-4 bg-slate-300 dark:bg-slate-600 rounded"></div>
                  <div className="w-16 h-4 bg-slate-300 dark:bg-slate-600 rounded"></div>
                </div>
                <div className="w-16 h-6 bg-slate-300 dark:bg-slate-600 rounded-full"></div>
              </div>

              {/* 标题骨架 */}
              <div className="space-y-2 mb-4">
                <div className="w-full h-6 bg-slate-300 dark:bg-slate-600 rounded"></div>
                <div className="w-3/4 h-6 bg-slate-300 dark:bg-slate-600 rounded"></div>
              </div>

              {/* 摘要骨架 */}
              <div className="space-y-2 mb-6">
                <div className="w-full h-4 bg-slate-200 dark:bg-slate-700 rounded"></div>
                <div className="w-full h-4 bg-slate-200 dark:bg-slate-700 rounded"></div>
                <div className="w-2/3 h-4 bg-slate-200 dark:bg-slate-700 rounded"></div>
              </div>
            </div>

            {/* 底部区域骨架 */}
            <div className="flex items-center justify-between pt-4 border-t border-slate-200/50 dark:border-slate-700/50">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-slate-300 dark:bg-slate-600 rounded-full"></div>
                <div className="w-16 h-4 bg-slate-300 dark:bg-slate-600 rounded"></div>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-slate-200 dark:bg-slate-700 rounded-full"></div>
                <div className="w-8 h-8 bg-slate-200 dark:bg-slate-700 rounded-full"></div>
                <div className="w-24 h-10 bg-slate-300 dark:bg-slate-600 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
      </article>
    </div>
  );
}

export function PostListSkeleton({ count = 3 }: { count?: number }) {
  return (
    <div className="space-y-6">
      {Array.from({ length: count }).map((_, index) => (
        <PostSkeleton key={index} />
      ))}
    </div>
  );
}
