import { SITE_NAME, SITE_DESCRIPTION } from "@/lib/constants";
import Link from "next/link";

export function HeroSection() {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* 背景装饰元素 */}
      <div className="absolute inset-0 -z-10">
        {/* 几何图形装饰 */}
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-blue-500 rounded-full animate-ping"></div>
        <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-purple-500 rounded-full animate-ping animation-delay-1000"></div>
        <div className="absolute bottom-1/3 left-1/3 w-1.5 h-1.5 bg-cyan-500 rounded-full animate-ping animation-delay-2000"></div>
        
        {/* 浮动代码片段 */}
        <div className="absolute top-20 right-20 opacity-10 dark:opacity-5 text-xs font-mono text-slate-600 dark:text-slate-400 animate-float">
          <div>const developer = {`{`}</div>
          <div>  passion: &quot;coding&quot;,</div>
          <div>  mission: &quot;share&quot;</div>
          <div>{`}`}</div>
        </div>
        
        <div className="absolute bottom-32 left-20 opacity-10 dark:opacity-5 text-xs font-mono text-slate-600 dark:text-slate-400 animate-float animation-delay-3000">
          <div>function learn() {`{`}</div>
          <div>  return &quot;never stop&quot;;</div>
          <div>{`}`}</div>
        </div>
      </div>

      <div className="container mx-auto px-4 text-center">
        {/* 欢迎标签 */}
        <div className="mb-8 animate-fade-in">
          <span className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 text-blue-700 dark:text-blue-300 rounded-full text-sm font-medium border border-blue-200/50 dark:border-blue-700/50">
            <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
            👨‍💻 欢迎来到代码世界
          </span>
        </div>

        {/* 主标题 */}
        <h1 className="text-6xl md:text-8xl lg:text-9xl font-black tracking-tight leading-none mb-8 animate-fade-in animation-delay-200">
          <span className="bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 dark:from-white dark:via-blue-100 dark:to-white bg-clip-text text-transparent">
            {SITE_NAME}
          </span>
        </h1>

        {/* 副标题 */}
        <p className="text-xl md:text-2xl lg:text-3xl text-slate-600 dark:text-slate-300 leading-relaxed mb-6 max-w-4xl mx-auto animate-fade-in animation-delay-400">
          {SITE_DESCRIPTION}
        </p>

        {/* 标语 */}
        <p className="text-lg md:text-xl text-slate-500 dark:text-slate-400 mb-12 animate-fade-in animation-delay-600">
          <span className="font-semibold text-blue-600 dark:text-blue-400">撸代码</span>
          <span className="mx-2">•</span>
          <span className="font-semibold text-purple-600 dark:text-purple-400">学技术</span>
          <span className="mx-2">•</span>
          <span className="font-semibold text-cyan-600 dark:text-cyan-400">分享经验</span>
        </p>

        {/* 技术标签云 */}
        <div className="flex flex-wrap justify-center gap-3 mb-12 animate-fade-in animation-delay-800">
          {[
            { name: 'React', color: 'from-blue-500 to-cyan-500' },
            { name: 'Next.js', color: 'from-slate-700 to-slate-900' },
            { name: 'TypeScript', color: 'from-blue-600 to-blue-700' },
            { name: 'Node.js', color: 'from-green-600 to-green-700' },
            { name: 'Python', color: 'from-yellow-500 to-blue-600' },
            { name: 'AI/ML', color: 'from-purple-500 to-pink-500' }
          ].map((tech, index) => (
            <span
              key={tech.name}
              className={`px-4 py-2 bg-gradient-to-r ${tech.color} text-white rounded-full text-sm font-medium shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 cursor-default`}
              style={{ animationDelay: `${800 + index * 100}ms` }}
            >
              {tech.name}
            </span>
          ))}
        </div>

        {/* 行动按钮 */}
        <div className="flex flex-col sm:flex-row gap-6 justify-center items-center animate-fade-in animation-delay-1000">
          <Link
            href="#featured"
            className="btn-glow group px-8 py-4 text-lg font-semibold"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            开始探索
            <svg className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>

          <Link
            href="#about"
            className="btn-gradient-border group px-8 py-4 text-lg font-semibold"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            了解更多
          </Link>

          <a
            href="https://github.com"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-2 px-6 py-3 text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white transition-colors duration-300 group"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
            </svg>
            <span className="group-hover:underline">GitHub</span>
          </a>
        </div>

        {/* 滚动提示 */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="flex flex-col items-center gap-2 text-slate-400 dark:text-slate-500">
            <span className="text-sm">向下滚动</span>
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </div>
        </div>
      </div>
    </section>
  );
}
