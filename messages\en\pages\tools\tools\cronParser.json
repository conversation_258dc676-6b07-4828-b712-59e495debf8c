{"title": "⏱️ Cron Expression Parser Tool", "description": "Online parsing and validation of cron expressions, view execution patterns and next run times", "breadcrumb": {"home": "Home", "tools": "Toolbox", "current": "Cron Expression Parser"}, "input": {"title": "Cron Expression Input", "description": "Enter standard 5-field cron expression: minute hour day month weekday", "placeholder": "e.g.: 0 9 * * 1-5", "label": "Cron Expression"}, "fields": {"names": ["Minute", "Hour", "Day", "Month", "Weekday"], "ranges": ["0-59", "0-23", "1-31", "1-12", "0-7 (0 and 7 both represent Sunday)"]}, "result": {"title": "<PERSON><PERSON>t", "fieldsTitle": "Field Analysis", "patternTitle": "Execution Pattern", "nextRunsTitle": "Estimated Execution Times (Examples)", "description": "Expression Description", "nextRuns": "Next Execution Times"}, "examples": {"title": "Common Examples", "description": "Click examples to quickly fill in", "list": [{"expression": "0 0 * * *", "description": "Execute daily at midnight"}, {"expression": "0 9 * * 1-5", "description": "Execute at 9 AM on weekdays"}, {"expression": "*/15 * * * *", "description": "Execute every 15 minutes"}, {"expression": "0 0 1 * *", "description": "Execute at midnight on the 1st of every month"}, {"expression": "0 0 * * 0", "description": "Execute every Sunday at midnight"}, {"expression": "30 2 * * *", "description": "Execute daily at 2:30 AM"}, {"expression": "0 */6 * * *", "description": "Execute every 6 hours"}, {"expression": "0 0 1 1 *", "description": "Execute on January 1st every year"}]}, "syntax": {"title": "Syntax Guide", "format": "Format:", "formatValue": "minute hour day month weekday", "specialChars": "Special Characters:", "chars": {"asterisk": "any value", "question": "no specific value (day and weekday only)", "dash": "range (e.g. 1-5)", "comma": "list (e.g. 1,3,5)", "slash": "step (e.g. */5)"}}, "actions": {"parse": "Parse", "clear": "Clear", "copy": "Copy", "copyTitle": "Copy"}, "errors": {"invalidFields": "Cron expression must contain 5 fields: minute hour day month weekday", "invalidFormat": "Expression format error, please check the value ranges of each field", "copyFailed": "Co<PERSON> failed", "noMatches": "No matching execution times in the next 7 days"}, "weekdays": ["Sun", "Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat", "Sun"], "timeDescriptions": {"executeTime": "Execution time: ", "everyMinute": "every minute", "everyNMinutes": "every {n} minutes", "minuteN": "at minute {n}", "everyHour": " of every hour", "everyNHours": ", every {n} hours", "hourN": ", at {n} o'clock", "dayAndWeek": ", on day {day} of every month or every {weekday}", "dayN": ", on day {day} of every month", "weekN": ", every {weekday}", "monthN": ", in {month}", "anyValue": "any value"}, "status": {"valid": "<PERSON><PERSON>", "invalid": "Invalid"}, "meta": {"title": "Cron Expression Parser - Online Cron Parsing Tool", "description": "Free online Cron expression parsing tool with visual schedule and next execution time prediction."}}